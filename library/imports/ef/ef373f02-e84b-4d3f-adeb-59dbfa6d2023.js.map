{"version": 3, "sources": ["assets/scripts/game/GameScoreController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,sEAAiE;AAE3D,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEAyQC;QAtQG,iBAAW,GAAY,IAAI,CAAC,CAAE,SAAS;QAEvC,oBAAc,GAAc,IAAI,CAAC,CAAE,uBAAuB;QAElD,uBAAiB,GAA4B,EAAE,CAAC,CAAC,UAAU;;QA2PnE;;;;WAIG;QAEH,iBAAiB;IACrB,CAAC;IAhQG,eAAe;IAEf,oCAAM,GAAN;QACI,qBAAqB;IACzB,CAAC;IAED,mCAAK,GAAL;QACI,sBAAsB;IAC1B,CAAC;IAED;;;OAGG;IACH,6CAAe,GAAf;QAGI,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC9D,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACnE,OAAO;SACV;QAED,kBAAkB;QAClB,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACvD,OAAO;SACV;QAED,eAAe;QACf,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QAGlE,wBAAwB;QACxB,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;YACtB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;gBACjD,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aAClB;QAEL,CAAC,CAAC,CAAC;QAEH,YAAY;QACZ,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;QACrC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhC,IAAI,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,+BAAqB,CAAC,CAAC;YAC/D,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC7C,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAErC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;aACtD;SACJ;IAGL,CAAC;IAED;;;OAGG;IACH,iDAAmB,GAAnB;QAEI,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,yCAAW,GAAX;QACI,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QAGlE,cAAc;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBACnC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAE/C;SACJ;IACL,CAAC;IAED;;;;OAIG;IACH,+CAAiB,GAAjB,UAAkB,MAAc,EAAE,KAAa;QAC3C,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QACvE,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,MAAM,EAAtB,CAAsB,CAAC,CAAC;QAElE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC/D,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAExD;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,oEAAqB,MAAM,oBAAe,SAAW,CAAC,CAAC;SACvE;IACL,CAAC;IAED;;OAEG;IACH,6CAAe,GAAf;QACI,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,OAAO;SACV;QAED,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QAGvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;SAE9D;IACL,CAAC;IAED;;;;OAIG;IACH,sDAAwB,GAAxB,UAAyB,SAAiB;QACtC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC7D,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,sDAAwB,GAAxB,UAAyB,IAA4B;;QAGjD,YAAY;QACZ,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,OAAO;SACV;QAED,YAAY;QACZ,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QACvE,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAA3B,CAA2B,CAAC,CAAC;QAEvE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAG/D,mBAAmB;YACnB,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YAGzC,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE/D,uBAAuB;YACvB,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAG3D,uCAAuC;YACvC,IAAI,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;YACzE,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;SAGlD;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gFAAuB,IAAI,CAAC,MAAM,oBAAe,SAAS,kCAAmB,IAAI,CAAC,iBAAiB,CAAC,MAAQ,CAAC,CAAC;YAE3H,eAAe;YAEf,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;YAE1B,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;OAIG;IACK,uDAAyB,GAAjC,UAAkC,SAAiB,EAAE,UAAkB;QAGnE,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC7D,IAAI,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAExD,+CAA+C;YAC/C,sDAAsD;YACtD,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAE5C;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iDAAY,SAAS,0CAAY,IAAI,CAAC,iBAAiB,CAAC,MAAQ,CAAC,CAAC;SAClF;IACL,CAAC;IAED;;;;OAIG;IACH,8CAAgB,GAAhB,UAAiB,MAAc,EAAE,WAAoB;QACjD,OAAO,CAAC,GAAG,CAAC,kDAAgD,MAAM,sBAAiB,WAAa,CAAC,CAAC;QAElG,YAAY;QACZ,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpC,OAAO;SACV;QAED,YAAY;QACZ,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QACvE,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,MAAM,EAAtB,CAAsB,CAAC,CAAC;QAElE,OAAO,CAAC,GAAG,CAAC,kDAAkB,MAAM,oBAAe,SAAS,mCAAU,KAAK,CAAC,MAAM,kCAAmB,IAAI,CAAC,iBAAiB,CAAC,MAAQ,CAAC,CAAC;QAEtI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC/D,kBAAkB;YAClB,OAAO,CAAC,GAAG,CAAC,oCAAwB,SAAS,6BAAwB,WAAW,MAAG,CAAC,CAAC;YACrF,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;SAGrE;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gFAAuB,MAAM,oBAAe,SAAS,kCAAmB,IAAI,CAAC,iBAAiB,CAAC,MAAQ,CAAC,CAAC;SACzH;IACL,CAAC;IA7PD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACU;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACa;IALhB,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAyQvC;IAAD,0BAAC;CAzQD,AAyQC,CAzQgD,EAAE,CAAC,SAAS,GAyQ5D;kBAzQoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser, NoticeFirstChoiceBonus } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport PlayerScoreController from \"../pfb/PlayerScoreController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class GameScoreController extends cc.Component {\n\n    @property(cc.Node)\n    scoreLayout: cc.Node = null;  // 分数布局容器\n    @property(cc.Prefab)\n    playerScorePfb: cc.Prefab = null;  // player_score_pfb 预制体\n\n    private _scoreControllers: PlayerScoreController[] = []; // 分数控制器数组\n\n    // onLoad () {}\n\n    onLoad() {\n        // 初始化时不自动创建界面，等待游戏数据\n    }\n\n    start() {\n        // 不在start中自动创建，等待外部调用\n    }\n\n    /**\n     * 创建分数显示界面\n     * 只使用后端传回来的真实游戏数据\n     */\n    createScoreView() {\n      \n\n        // 检查必要的组件是否存在\n        if (!this.scoreLayout) {\n            console.error(\"scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性\");\n            return;\n        }\n\n        if (!this.playerScorePfb) {\n            console.error(\"playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性\");\n            return;\n        }\n\n        // 只使用后端传回来的真实游戏数据\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息\");\n            return;\n        }\n\n        // 获取后端传回来的用户数据\n        let users: RoomUser[] = GlobalBean.GetInstance().adjustUserData();\n        \n\n        // 确保所有用户都有score字段，初始化为0\n        users.forEach((user, index) => {\n            if (user.score === undefined || user.score === null) {\n                user.score = 0;\n            }\n            \n        });\n\n        // 清空现有的分数显示\n        this.scoreLayout.removeAllChildren();\n        this._scoreControllers = [];\n\n        // 根据后端用户数据生成分数预制体\n        for (let i = 0; i < users.length; i++) {\n            const item = cc.instantiate(this.playerScorePfb);\n            this.scoreLayout.addChild(item);\n\n            let scoreController = item.getComponent(PlayerScoreController);\n            if (scoreController) {\n                this._scoreControllers.push(scoreController);\n                scoreController.setData(users[i]);\n                \n            } else {\n                console.error(\"预制体上没有找到 PlayerScoreController 组件\");\n            }\n        }\n\n       \n    }\n\n    /**\n     * 初始化分数界面\n     * 当收到 NoticeStartGame 消息后调用此方法\n     */\n    initializeScoreView() {\n        \n        this.createScoreView();\n    }\n\n    /**\n     * 设置游戏数据\n     * 更新所有玩家的分数显示\n     */\n    setGameData() {\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法设置分数数据\");\n            return;\n        }\n\n        let users: RoomUser[] = GlobalBean.GetInstance().adjustUserData();\n       \n\n        // 更新所有玩家的分数显示\n        for (let i = 0; i < users.length; i++) {\n            if (i < this._scoreControllers.length) {\n                this._scoreControllers[i].setData(users[i]);\n               \n            }\n        }\n    }\n\n    /**\n     * 更新特定玩家的分数\n     * @param userId 玩家ID\n     * @param score 新的分数\n     */\n    updatePlayerScore(userId: string, score: number) {\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法更新玩家分数\");\n            return;\n        }\n\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n        const userIndex = users.findIndex(user => user.userId === userId);\n\n        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {\n            this._scoreControllers[userIndex].updateScore(score);\n     \n        } else {\n            console.warn(`找不到玩家或控制器: userId=${userId}, userIndex=${userIndex}`);\n        }\n    }\n\n    /**\n     * 更新所有玩家分数\n     */\n    updateAllScores() {\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法更新所有玩家分数\");\n            return;\n        }\n\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n       \n\n        for (let i = 0; i < users.length && i < this._scoreControllers.length; i++) {\n            this._scoreControllers[i].updateScore(users[i].score || 0);\n           \n        }\n    }\n\n    /**\n     * 获取指定索引的PlayerScoreController\n     * @param userIndex 用户索引\n     * @returns PlayerScoreController 或 null\n     */\n    getPlayerScoreController(userIndex: number): any {\n        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {\n            return this._scoreControllers[userIndex];\n        }\n        return null;\n    }\n\n    /**\n     * 处理首选玩家奖励通知\n     * @param data NoticeFirstChoiceBonus 消息数据\n     */\n    onNoticeFirstChoiceBonus(data: NoticeFirstChoiceBonus) {\n       \n\n        // 检查是否有游戏数据\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法处理首选玩家奖励\");\n            return;\n        }\n\n        // 查找对应的玩家索引\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n        const userIndex = users.findIndex(user => user.userId === data.userId);\n\n        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {\n            \n\n            // 1. 更新玩家的总分数到全局数据\n            users[userIndex].score = data.totalScore;\n           \n\n            // 2. 更新分数显示 - 显示新的总分\n            this._scoreControllers[userIndex].updateScore(data.totalScore);\n           \n            // 3. 显示加分效果动画 - 显示奖励分数\n            this.showAddScoreWithAnimation(userIndex, data.bonusScore);\n           \n\n            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb\n            let currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n            let isMyself = (data.userId === currentUserId);\n\n         \n        } else {\n            console.warn(`找不到对应的玩家控制器: userId=${data.userId}, userIndex=${userIndex}, controllers长度=${this._scoreControllers.length}`);\n\n            // 打印所有用户信息用于调试\n           \n            users.forEach((user, index) => {\n               \n            });\n        }\n    }\n\n    /**\n     * 显示加分效果动画\n     * @param userIndex 用户索引\n     * @param bonusScore 奖励分数\n     */\n    private showAddScoreWithAnimation(userIndex: number, bonusScore: number) {\n\n\n        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {\n            let scoreController = this._scoreControllers[userIndex];\n\n            // 调用PlayerScoreController的showAddScore方法显示加分效果\n            // 这会在player_score_pfb的addscore/change_score中显示\"+1\"等文本\n            scoreController.showAddScore(bonusScore);\n\n        } else {\n            console.warn(`无效的用户索引: ${userIndex}, 控制器数量: ${this._scoreControllers.length}`);\n        }\n    }\n\n    /**\n     * 处理AI托管状态变更\n     * @param userId 用户ID\n     * @param isAIManaged 是否进入AI托管状态\n     */\n    onAIStatusChange(userId: string, isAIManaged: boolean) {\n        console.log(`GameScoreController.onAIStatusChange: userId=${userId}, isAIManaged=${isAIManaged}`);\n\n        // 检查是否有游戏数据\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法处理AI托管状态变更\");\n            return;\n        }\n\n        // 查找对应的玩家索引\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n        const userIndex = users.findIndex(user => user.userId === userId);\n\n        console.log(`查找玩家索引: userId=${userId}, userIndex=${userIndex}, 总用户数=${users.length}, controllers长度=${this._scoreControllers.length}`);\n\n        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {\n            // 设置对应玩家的AI托管状态显示\n            console.log(`调用 _scoreControllers[${userIndex}].setAIManagedStatus(${isAIManaged})`);\n            this._scoreControllers[userIndex].setAIManagedStatus(isAIManaged);\n\n\n        } else {\n            console.warn(`找不到对应的玩家控制器: userId=${userId}, userIndex=${userIndex}, controllers长度=${this._scoreControllers.length}`);\n        }\n    }\n\n    /**\n     * 更新player_game_pfb中的change_score显示\n     * @param userId 用户ID\n     * @param bonusScore 奖励分数\n     */\n\n    // update (dt) {}\n}\n"]}