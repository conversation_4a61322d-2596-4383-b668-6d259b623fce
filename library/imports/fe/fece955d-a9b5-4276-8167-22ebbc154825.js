"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // AI托管显示节点
        _this.aiManagedNode = null; //AI托管显示节点
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        console.log("PlayerScoreController.start: \u7EC4\u4EF6\u542F\u52A8");
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
        // 不要默认隐藏AI托管节点，让setData方法来决定是否显示
        // this.hideAIManagedNode();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
            this.hideAIManagedNode();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
            // 设置AI托管状态显示
            if (user.isAIManaged !== undefined) {
                console.log("setData: \u8BBE\u7F6E\u73A9\u5BB6 " + user.nickName + " (" + user.userId + ") AI\u6258\u7BA1\u72B6\u6001: " + user.isAIManaged);
                this.setAIManagedStatus(user.isAIManaged);
            }
            else {
                // 如果没有AI托管状态信息，默认隐藏托管节点
                this.hideAIManagedNode();
            }
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    /**
     * 显示AI托管节点
     */
    PlayerScoreController.prototype.showAIManagedNode = function () {
        var _a;
        if (this.aiManagedNode) {
            this.aiManagedNode.active = true;
            // 详细调试信息
            console.log("AI\u6258\u7BA1\u8282\u70B9\u8C03\u8BD5\u4FE1\u606F:");
            console.log("- \u8282\u70B9\u540D\u79F0: " + this.aiManagedNode.name);
            console.log("- active\u72B6\u6001: " + this.aiManagedNode.active);
            console.log("- \u7236\u8282\u70B9\u5B58\u5728: " + !!this.aiManagedNode.parent);
            console.log("- \u7236\u8282\u70B9active: " + ((_a = this.aiManagedNode.parent) === null || _a === void 0 ? void 0 : _a.active));
            console.log("- \u8282\u70B9\u4F4D\u7F6E: x=" + this.aiManagedNode.x + ", y=" + this.aiManagedNode.y);
            console.log("- \u8282\u70B9\u5927\u5C0F: width=" + this.aiManagedNode.width + ", height=" + this.aiManagedNode.height);
            console.log("- \u8282\u70B9\u900F\u660E\u5EA6: " + this.aiManagedNode.opacity);
            console.log("- \u8282\u70B9\u7F29\u653E: scaleX=" + this.aiManagedNode.scaleX + ", scaleY=" + this.aiManagedNode.scaleY);
            // 检查父节点链
            var parent = this.aiManagedNode.parent;
            var level = 1;
            while (parent && level <= 3) {
                console.log("- \u7236\u8282\u70B9" + level + ": " + parent.name + ", active=" + parent.active + ", opacity=" + parent.opacity);
                parent = parent.parent;
                level++;
            }
            // 检查兄弟节点，看是否被遮挡
            if (this.aiManagedNode.parent) {
                var siblings = this.aiManagedNode.parent.children;
                var myIndex = siblings.indexOf(this.aiManagedNode);
                console.log("- \u8282\u70B9\u5728\u7236\u8282\u70B9\u4E2D\u7684\u7D22\u5F15: " + myIndex + "/" + siblings.length);
                console.log("- \u5144\u5F1F\u8282\u70B9\u5217\u8868:");
                siblings.forEach(function (sibling, index) {
                    console.log("  [" + index + "] " + sibling.name + ": active=" + sibling.active + ", zIndex=" + (sibling.zIndex || 0));
                });
            }
        }
    };
    /**
     * 隐藏AI托管节点
     */
    PlayerScoreController.prototype.hideAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = false;
        }
    };
    /**
     * 设置AI托管状态
     * @param isManaged 是否进入AI托管状态
     */
    PlayerScoreController.prototype.setAIManagedStatus = function (isManaged) {
        var _this = this;
        var _a, _b, _c, _d;
        console.log("PlayerScoreController.setAIManagedStatus: \u73A9\u5BB6 " + (((_a = this.currentUser) === null || _a === void 0 ? void 0 : _a.nickName) || '未知') + " (" + (((_b = this.currentUser) === null || _b === void 0 ? void 0 : _b.userId) || '未知') + ") \u8BBE\u7F6EAI\u6258\u7BA1\u72B6\u6001: " + isManaged);
        console.log("aiManagedNode \u5B58\u5728: " + !!this.aiManagedNode);
        if (isManaged) {
            this.showAIManagedNode();
            console.log("AI\u6258\u7BA1\u8282\u70B9\u5DF2\u663E\u793A\uFF0Cactive\u72B6\u6001: " + ((_c = this.aiManagedNode) === null || _c === void 0 ? void 0 : _c.active));
            // 强制刷新节点显示状态
            if (this.aiManagedNode) {
                this.scheduleOnce(function () {
                    console.log("\u5EF6\u8FDF\u68C0\u67E5AI\u6258\u7BA1\u8282\u70B9\u72B6\u6001: active=" + _this.aiManagedNode.active + ", \u53EF\u89C1=" + _this.aiManagedNode.activeInHierarchy);
                    // 检查Sprite组件
                    var sprite = _this.aiManagedNode.getComponent(cc.Sprite);
                    if (sprite) {
                        console.log("- Sprite\u7EC4\u4EF6\u5B58\u5728: enabled=" + sprite.enabled + ", spriteFrame=" + !!sprite.spriteFrame);
                    }
                    else {
                        console.log("- \u6CA1\u6709Sprite\u7EC4\u4EF6");
                    }
                    // 尝试强制刷新
                    _this.aiManagedNode.active = false;
                    _this.aiManagedNode.active = true;
                    // 再次检查状态
                    console.log("\u5F3A\u5236\u5237\u65B0\u540E: active=" + _this.aiManagedNode.active + ", \u53EF\u89C1=" + _this.aiManagedNode.activeInHierarchy);
                }, 0.1);
            }
        }
        else {
            this.hideAIManagedNode();
            console.log("AI\u6258\u7BA1\u8282\u70B9\u5DF2\u9690\u85CF\uFF0Cactive\u72B6\u6001: " + ((_d = this.aiManagedNode) === null || _d === void 0 ? void 0 : _d.active));
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "aiManagedNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();