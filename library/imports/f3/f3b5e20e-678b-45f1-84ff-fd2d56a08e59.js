"use strict";
cc._RF.push(module, 'f3b5eIOZ4tF8YT//S1WoI5Z', 'CongratsDialogController');
// scripts/game/CongratsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var CongratsItemController_1 = require("../pfb/CongratsItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//结算页面
var CongratsDialogController = /** @class */ (function (_super) {
    __extends(CongratsDialogController, _super);
    function CongratsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.contentLay = null;
        _this.backBtn = null;
        _this.congratsItem = null; //列表的 item
        _this.layoutNode = null; //存放列表的布局
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.backCallback = null; //隐藏弹窗的回调
        _this.seconds = 10; //倒计时 10 秒
        return _this;
    }
    CongratsDialogController.prototype.onLoad = function () {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    CongratsDialogController.prototype.onEnable = function () {
        this.updateCountdownLabel(this.seconds);
        //Tools.setCountDownTimeLabel(this.backBtn)
    };
    CongratsDialogController.prototype.start = function () {
        var _this = this;
        //backBtn 按钮点击事件
        Tools_1.Tools.greenButton(this.backBtn, function () {
            _this.hide(true);
        });
        // 设置倒计时标签的固定位置
        this.setFixedCountdownPosition();
        // 设置整个按钮的点击效果（包括按钮背景和倒计时标签的统一反馈）
        this.setupButtonWithCountdownEffect();
    };
    CongratsDialogController.prototype.show = function (noticeSettlement, backCallback) {
        var _this = this;
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 每次显示时重新设置倒计时标签的固定位置，确保位置正确
        this.setFixedCountdownPosition();
        this._setData(noticeSettlement);
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .call(function () {
            // 动画完成后再次设置位置，确保位置正确
            _this.setFixedCountdownPosition();
        })
            .start();
    };
    CongratsDialogController.prototype._setData = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            this.startCountdown(10); // 仍然启动倒计时
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) {
            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币
            if ('coin' in userList[index]) {
                // UserSettlement 类型，直接使用 coin 字段
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = userList[index].coin;
            }
            else if ('coinChg' in userList[index]) {
                // PlayerFinalResult 类型，使用 coinChg 字段更新金币
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin += userList[index].coinChg;
            }
        }
        this.layoutNode.removeAllChildren();
        var _loop_1 = function (i) {
            var item = cc.instantiate(this_1.congratsItem);
            var data = userList[i];
            this_1.layoutNode.addChild(item);
            setTimeout(function () {
                item.getComponent(CongratsItemController_1.default).createData(data, GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        };
        var this_1 = this;
        for (var i = 0; i < userList.length; ++i) {
            _loop_1(i);
        }
        this.startCountdown(10); //倒计时 10 秒
    };
    // bool 在隐藏的时候是否返回大厅
    CongratsDialogController.prototype.hide = function (bool) {
        var _this = this;
        if (bool === void 0) { bool = false; }
        if (this.backCallback) {
            this.backCallback();
        }
        GameMgr_1.GameMgr.Console.Log('隐藏结算页面');
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
            if (bool) {
                GlobalBean_1.GlobalBean.GetInstance().cleanData();
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                    'data': { 'type': 2 } //2是结算弹窗跳转的
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        })
            .start();
    };
    CongratsDialogController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    };
    CongratsDialogController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 在开始倒计时前再次确保位置正确
        this.setFixedCountdownPosition();
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                _this.hide(true);
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    };
    CongratsDialogController.prototype.updateCountdownLabel = function (seconds) {
        var _this = this;
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
            // 每次更新倒计时文本后，重新设置位置，确保位置始终正确
            this.scheduleOnce(function () {
                _this.setFixedCountdownPosition();
            }, 0.01);
        }
    };
    // 设置固定的倒计时位置，左括号始终对准back文字的右边
    CongratsDialogController.prototype.setFixedCountdownPosition = function () {
        if (!this.backBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - backBtn不存在");
            return;
        }
        // 重新获取节点，确保引用是最新的
        var btn = this.backBtn.getChildByName('button_label');
        var timeBtn = this.backBtn.getChildByName('buttonLabel_time');
        if (!btn || !timeBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 缺少子节点", {
                hasBtn: !!btn,
                hasTimeBtn: !!timeBtn
            });
            return;
        }
        // 重新获取倒计时标签组件，确保引用正确
        this.countdownTimeLabel = timeBtn.getComponent(cc.Label);
        if (!this.countdownTimeLabel) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 无法获取Label组件");
            return;
        }
        // 延迟一帧执行，确保所有UI初始化完成
        this.scheduleOnce(function () {
            // 强制设置锚点为左对齐，确保左括号位置固定
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5; // 确保垂直居中
            // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置
            var fixedLeftPos = btn.position.x + btn.width / 2 - 10; // 5像素间距
            // 直接设置位置
            timeBtn.setPosition(fixedLeftPos, 0);
            // 再次强制设置锚点，防止被其他代码重置
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5;
        }, 0.01);
    };
    // 设置整个按钮的统一点击效果（按钮背景和倒计时标签都有反馈）
    CongratsDialogController.prototype.setupButtonWithCountdownEffect = function () {
        var _this = this;
        if (!this.backBtn) {
            console.warn("CongratsDialogController: backBtn未设置，无法添加点击效果");
            return;
        }
        // 获取按钮的子节点
        var btnColorNormal = this.backBtn.getChildByName('btn_color_normal');
        var buttonLabel = this.backBtn.getChildByName('button_label');
        if (!btnColorNormal || !buttonLabel) {
            console.warn("CongratsDialogController: 按钮结构不完整，无法添加点击效果");
            return;
        }
        var label = buttonLabel.getComponent(cc.Label);
        var labelOutline = buttonLabel.getComponent(cc.LabelOutline);
        if (!label || !labelOutline) {
            console.warn("CongratsDialogController: 按钮标签组件不完整");
            return;
        }
        // 记录倒计时标签的原始字体大小
        var originalCountdownFontSize = 36;
        var originalCountdownLineHeight = 36;
        if (this.countdownTimeLabel) {
            originalCountdownFontSize = this.countdownTimeLabel.fontSize;
            originalCountdownLineHeight = this.countdownTimeLabel.lineHeight;
        }
        // 自定义按钮点击效果，同时控制倒计时标签
        Tools_1.Tools.setTouchEvent(btnColorNormal, 
        // 按下时：按钮和倒计时标签都变暗
        function (node) {
            // 按钮背景变暗
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenPressed);
            // 按钮文字变暗
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenPressedColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            // 倒计时标签也变暗（字体缩小效果）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小缩小（从原始大小缩小2）
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize - 2;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight - 2;
                // 轮廓颜色变化（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenPressedColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色变暗
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            }
        }, 
        // 抬起时：恢复正常并执行点击逻辑
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
            // 执行点击逻辑
            _this.hide(true);
        }, 
        // 取消时：恢复正常
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
        });
    };
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Prefab)
    ], CongratsDialogController.prototype, "congratsItem", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "layoutNode", void 0);
    CongratsDialogController = __decorate([
        ccclass
    ], CongratsDialogController);
    return CongratsDialogController;
}(cc.Component));
exports.default = CongratsDialogController;

cc._RF.pop();