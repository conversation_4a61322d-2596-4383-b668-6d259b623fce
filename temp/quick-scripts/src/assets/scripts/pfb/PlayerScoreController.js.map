{"version": 3, "sources": ["assets/scripts/pfb/PlayerScoreController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,uDAAkD;AAClD,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAmD,yCAAY;IAA/D;QAAA,qEAyQC;QAtQG,YAAM,GAAY,IAAI,CAAC,CAAG,IAAI;QAE9B,eAAS,GAAa,IAAI,CAAC,CAAE,MAAM;QAEnC,WAAW;QAEX,eAAS,GAAY,IAAI,CAAC,CAAE,sBAAsB;QAElD,mBAAa,GAAY,IAAI,CAAC,CAAE,4BAA4B;QAE5D,UAAU;QAEV,kBAAY,GAAY,IAAI,CAAC,CAAE,iBAAiB;QAEhD,kBAAY,GAAY,IAAI,CAAC,CAAE,oBAAoB;QAEnD,WAAW;QAEX,mBAAa,GAAY,IAAI,CAAC,CAAE,UAAU;QAE1C,SAAS;QACD,iBAAW,GAAa,IAAI,CAAC;;IAiPzC,CAAC;IA/OG,qCAAK,GAAL;QAEI,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,iCAAiC;QACjC,4BAA4B;IAChC,CAAC;IAED;;;OAGG;IACH,uCAAO,GAAP,UAAQ,IAAc;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,OAAO;YACP,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;aAAM;YACH,UAAU;YACV,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,uBAAa,CAAC,CAAC;YAC/D,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;YAE1B,SAAS;YACT,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;YAElC,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;gBAEhC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC7C;iBAAM;gBACH,wBAAwB;gBACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAED;;;OAGG;IACH,2CAAW,GAAX,UAAY,KAAa;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9B,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAE7D,IAAI,QAAQ,EAAE;YACV,SAAS;YACT,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC3B;aAAM;YACH,WAAW;YACX,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;;OAGG;IACK,6CAAa,GAArB,UAAsB,MAAc;;QAChC,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QAC3E,OAAO,MAAM,KAAK,aAAa,CAAC;IACpC,CAAC;IAED;;;OAGG;IACK,2CAAW,GAAnB,UAAoB,KAAa;QAC7B,kBAAkB;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;YAC7B,sBAAsB;YACtB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC/D,IAAI,YAAY,EAAE;gBACd,IAAM,cAAc,GAAG,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC3D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;iBAC5C;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED;;;OAGG;IACK,+CAAe,GAAvB,UAAwB,KAAa;QACjC,kBAAkB;QAClB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;YACjC,yBAAyB;YACzB,IAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACzE,IAAI,eAAe,EAAE;gBACjB,IAAM,cAAc,GAAG,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC9D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;iBAC5C;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,uDAAuB,GAA/B;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;SACjC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED;;;OAGG;IACH,4CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBAoBC;QAnBG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAEhC,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;iBACrD;aACJ;YAED,QAAQ;YACR,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,KAAI,CAAC,YAAY,EAAE;oBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;iBACpC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;IACL,CAAC;IAED;;;OAGG;IACH,4CAAY,GAAZ,UAAa,QAAgB;QAA7B,iBAoBC;QAnBG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;YAEhC,4BAA4B;YAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,gBAAgB,EAAE;gBAClB,IAAM,cAAc,GAAG,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,cAAc,EAAE;oBAChB,cAAc,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;iBACrD;aACJ;YAED,QAAQ;YACR,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,KAAI,CAAC,YAAY,EAAE;oBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;iBACpC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;IACL,CAAC;IAED;;OAEG;IACH,gDAAgB,GAAhB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACH,iDAAiB,GAAjB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACH,iDAAiB,GAAjB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED;;;OAGG;IACH,kDAAkB,GAAlB,UAAmB,SAAkB;QAArC,iBAwBC;QAtBG,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAGzB,aAAa;YACb,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC;oBAId,SAAS;oBACT,KAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;oBAClC,KAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;oBAEjC,SAAS;gBAEb,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;SACJ;aAAM;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAE5B;IACL,CAAC;IArQD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;4DACQ;IAI3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACY;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACY;IArBb,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAyQzC;IAAD,4BAAC;CAzQD,AAyQC,CAzQkD,EAAE,CAAC,SAAS,GAyQ9D;kBAzQoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport NickNameLabel from \"../util/NickNameLabel\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class PlayerScoreController extends cc.Component {\n\n    @property(cc.Node)\n    avatar: cc.Node = null;   //头像\n    @property(cc.Label)\n    nameLabel: cc.Label = null;  //用户昵称\n\n    // 分数显示相关节点\n    @property(cc.Node)\n    scoreBgMy: cc.Node = null;  //我的分数背景节点 score_bg_my\n    @property(cc.Node)\n    scoreBgOthers: cc.Node = null;  //其他人的分数背景节点 score_bg_others\n\n    // 加减分效果节点\n    @property(cc.Node)\n    addScoreNode: cc.Node = null;  //加分背景节点 addscore\n    @property(cc.Node)\n    subScoreNode: cc.Node = null;  //减分背景节点 deductscore\n\n    // AI托管显示节点\n    @property(cc.Node)\n    aiManagedNode: cc.Node = null;  //AI托管显示节点\n\n    // 当前用户数据\n    private currentUser: RoomUser = null;\n\n    start () {\n    \n        // 初始化时隐藏所有加减分效果\n        this.hideScoreEffects();\n        // 不要默认隐藏AI托管节点，让setData方法来决定是否显示\n        // this.hideAIManagedNode();\n    }\n\n    /**\n     * 设置玩家数据\n     * @param user 房间用户数据\n     */\n    setData(user: RoomUser) {\n        this.currentUser = user;\n\n        if (user == null) {\n            // 清空数据\n            this.avatar.active = false;\n            this.nameLabel.string = \"\";\n            this.hideAllScoreBackgrounds();\n            this.hideScoreEffects();\n            this.hideAIManagedNode();\n        } else {\n            // 设置头像和昵称\n            Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);\n            let nicknameLabel = this.nameLabel.getComponent(NickNameLabel);\n            nicknameLabel.string = user.nickName;\n            this.avatar.active = true;\n\n            // 设置分数显示\n            this.updateScore(user.score || 0);\n\n            // 设置AI托管状态显示\n            if (user.isAIManaged !== undefined) {\n               \n                this.setAIManagedStatus(user.isAIManaged);\n            } else {\n                // 如果没有AI托管状态信息，默认隐藏托管节点\n                this.hideAIManagedNode();\n            }\n        }\n    }\n\n    /**\n     * 更新分数显示\n     * @param score 新的分数值\n     */\n    updateScore(score: number) {\n        if (!this.currentUser) return;\n\n        const isMyself = this.isCurrentUser(this.currentUser.userId);\n\n        if (isMyself) {\n            // 显示我的分数\n            this.showMyScore(score);\n        } else {\n            // 显示其他人的分数\n            this.showOthersScore(score);\n        }\n    }\n\n    /**\n     * 判断是否为当前登录用户\n     * @param userId 用户ID\n     */\n    private isCurrentUser(userId: string): boolean {\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        return userId === currentUserId;\n    }\n\n    /**\n     * 显示我的分数\n     * @param score 分数值\n     */\n    private showMyScore(score: number) {\n        // 显示我的分数背景，隐藏其他人的\n        if (this.scoreBgMy) {\n            this.scoreBgMy.active = true;\n            // 获取my_score文本节点并设置分数\n            const myScoreLabel = this.scoreBgMy.getChildByName(\"my_score\");\n            if (myScoreLabel) {\n                const labelComponent = myScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = score.toString();\n                }\n            }\n        }\n\n        if (this.scoreBgOthers) {\n            this.scoreBgOthers.active = false;\n        }\n    }\n\n    /**\n     * 显示其他人的分数\n     * @param score 分数值\n     */\n    private showOthersScore(score: number) {\n        // 显示其他人的分数背景，隐藏我的\n        if (this.scoreBgOthers) {\n            this.scoreBgOthers.active = true;\n            // 获取other_score文本节点并设置分数\n            const otherScoreLabel = this.scoreBgOthers.getChildByName(\"other_score\");\n            if (otherScoreLabel) {\n                const labelComponent = otherScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = score.toString();\n                }\n            }\n        }\n\n        if (this.scoreBgMy) {\n            this.scoreBgMy.active = false;\n        }\n    }\n\n    /**\n     * 隐藏所有分数背景\n     */\n    private hideAllScoreBackgrounds() {\n        if (this.scoreBgMy) {\n            this.scoreBgMy.active = false;\n        }\n        if (this.scoreBgOthers) {\n            this.scoreBgOthers.active = false;\n        }\n    }\n\n    /**\n     * 显示加分效果\n     * @param addValue 加分数值\n     */\n    showAddScore(addValue: number) {\n        if (this.addScoreNode) {\n            this.addScoreNode.active = true;\n\n            // 获取change_score文本节点并设置加分文本\n            const changeScoreLabel = this.addScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"+\" + addValue.toString();\n                }\n            }\n\n            // 1秒后隐藏\n            this.scheduleOnce(() => {\n                if (this.addScoreNode) {\n                    this.addScoreNode.active = false;\n                }\n            }, 1.0);\n        }\n    }\n\n    /**\n     * 显示减分效果\n     * @param subValue 减分数值\n     */\n    showSubScore(subValue: number) {\n        if (this.subScoreNode) {\n            this.subScoreNode.active = true;\n\n            // 获取change_score文本节点并设置减分文本\n            const changeScoreLabel = this.subScoreNode.getChildByName(\"change_score\");\n            if (changeScoreLabel) {\n                const labelComponent = changeScoreLabel.getComponent(cc.Label);\n                if (labelComponent) {\n                    labelComponent.string = \"-\" + subValue.toString();\n                }\n            }\n\n            // 1秒后隐藏\n            this.scheduleOnce(() => {\n                if (this.subScoreNode) {\n                    this.subScoreNode.active = false;\n                }\n            }, 1.0);\n        }\n    }\n\n    /**\n     * 隐藏加减分效果节点\n     */\n    hideScoreEffects() {\n        if (this.addScoreNode) {\n            this.addScoreNode.active = false;\n        }\n        if (this.subScoreNode) {\n            this.subScoreNode.active = false;\n        }\n    }\n\n    /**\n     * 显示AI托管节点\n     */\n    showAIManagedNode() {\n        if (this.aiManagedNode) {\n            this.aiManagedNode.active = true;        \n        }\n    }\n\n    /**\n     * 隐藏AI托管节点\n     */\n    hideAIManagedNode() {\n        if (this.aiManagedNode) {\n            this.aiManagedNode.active = false;\n        }\n    }\n\n    /**\n     * 设置AI托管状态\n     * @param isManaged 是否进入AI托管状态\n     */\n    setAIManagedStatus(isManaged: boolean) {\n        \n        if (isManaged) {\n            this.showAIManagedNode();\n            \n\n            // 强制刷新节点显示状态\n            if (this.aiManagedNode) {\n                this.scheduleOnce(() => {\n                   \n\n                   \n                    // 尝试强制刷新\n                    this.aiManagedNode.active = false;\n                    this.aiManagedNode.active = true;\n\n                    // 再次检查状态\n                   \n                }, 0.1);\n            }\n        } else {\n            this.hideAIManagedNode();\n           \n        }\n    }\n}\n"]}