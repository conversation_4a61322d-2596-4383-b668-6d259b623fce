
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":2,"./joui18n-script/LocalizedLabel":1,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":21,"./assets/meshTools/tools/MeshSdkApi":16,"./assets/meshTools/tools/Publish":3,"./assets/meshTools/tools/MeshSdk":17,"./assets/scripts/TipsDialogController":15,"./assets/scripts/ToastController":49,"./assets/scripts/GlobalManagerController":75,"./assets/scripts/bean/GameBean":4,"./assets/scripts/bean/GlobalBean":18,"./assets/scripts/bean/LanguageType":20,"./assets/scripts/bean/EnumBean":19,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":30,"./assets/scripts/common/GameTools":22,"./assets/scripts/common/MineConsole":24,"./assets/scripts/common/EventCenter":26,"./assets/scripts/game/BtnController":23,"./assets/scripts/game/CongratsDialogController":28,"./assets/scripts/game/GamePageController":27,"./assets/scripts/game/GameScoreController":39,"./assets/scripts/game/AIManagedDialogController":25,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":29,"./assets/scripts/game/Chess/HexSingleChessBoardController":41,"./assets/scripts/game/Chess/SingleChessBoardController":37,"./assets/scripts/game/Chess/ChessBoardController":33,"./assets/scripts/hall/HallCenterLayController":31,"./assets/scripts/hall/HallCreateRoomController":54,"./assets/scripts/hall/HallJoinRoomController":35,"./assets/scripts/hall/HallPageController":32,"./assets/scripts/hall/HallParentController":38,"./assets/scripts/hall/InfoDialogController":34,"./assets/scripts/hall/KickOutDialogController":36,"./assets/scripts/hall/LeaveDialogController":43,"./assets/scripts/hall/LevelSelectDemo":42,"./assets/scripts/hall/MatchParentController":40,"./assets/scripts/hall/PlayerLayoutController":48,"./assets/scripts/hall/SettingDialogController":53,"./assets/scripts/hall/TopUpDialogController":44,"./assets/scripts/hall/HallAutoController":45,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectExample":47,"./assets/scripts/hall/Level/LevelSelectPageController":46,"./assets/scripts/hall/Level/ScrollViewHelper":56,"./assets/scripts/hall/Level/LevelItemController":52,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/net/GameServerUrl":11,"./assets/scripts/net/HttpManager":58,"./assets/scripts/net/HttpUtils":50,"./assets/scripts/net/IHttpMsgBody":59,"./assets/scripts/net/MessageBaseBean":55,"./assets/scripts/net/MessageId":78,"./assets/scripts/net/WebSocketManager":51,"./assets/scripts/net/WebSocketTool":62,"./assets/scripts/net/ErrorCode":63,"./assets/scripts/pfb/InfoItemController":73,"./assets/scripts/pfb/InfoItemOneController":57,"./assets/scripts/pfb/MatchItemController":12,"./assets/scripts/pfb/PlayerGameController ":64,"./assets/scripts/pfb/PlayerScoreController":60,"./assets/scripts/pfb/SeatItemController":61,"./assets/scripts/pfb/CongratsItemController":66,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":69,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/util/BlockingQueue":67,"./assets/scripts/util/Config":77,"./assets/scripts/util/Dictionary":76,"./assets/scripts/util/LocalStorageManager":79,"./assets/scripts/util/NickNameLabel":65,"./assets/scripts/util/Tools":68,"./assets/scripts/util/AudioManager":72,"./assets/meshTools/MeshTools":70,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":71,"./assets/resources/i18n/zh_CN":74},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":56},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":70,"../../meshTools/Singleton":8,"../net/GameServerUrl":11},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../hall/LeaveDialogController":43,"../util/Tools":68,"../util/Config":77,"../GlobalManagerController":75,"../game/Chess/SingleChessBoardController":37,"../game/Chess/HexSingleChessBoardController":41,"../net/WebSocketManager":51,"../net/MessageId":78,"../common/GameMgr":30,"../common/EventCenter":26},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../util/NickNameLabel":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../common/GameMgr":30,"./StartUpCenterController":69},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./Config":77,"./Dictionary":76},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"./util/Config":77,"./util/Tools":68},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../MeshTools":70,"../BaseSDK":21,"../../scripts/net/MessageBaseBean":55,"../../scripts/common/GameMgr":30,"../../scripts/common/EventCenter":26,"MeshSdk":17},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":45},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../util/AudioManager":72,"../util/Config":77,"../util/LocalStorageManager":79},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../net/WebSocketManager":51,"../net/MessageId":78,"../util/Config":77},"path":"preview-scripts/assets/scripts/game/AIManagedDialogController.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":30},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"./CongratsDialogController":28,"./GameScoreController":39,"./AIManagedDialogController":25,"../bean/GlobalBean":18,"../hall/LeaveDialogController":43,"../util/Config":77,"../util/Tools":68,"../util/AudioManager":72,"./Chess/HexChessBoardController":29,"./Chess/ChessBoardController":33,"../pfb/PlayerGameController ":64,"../net/MessageId":78,"../net/WebSocketManager":51},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../bean/GlobalBean":18,"../common/EventCenter":26,"../common/GameMgr":30,"../net/MessageBaseBean":55,"../pfb/CongratsItemController":66,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../bean/GlobalBean":18,"../../pfb/PlayerGameController ":64},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":16,"./EventCenter":26,"./GameData":9,"./GameTools":22,"./MineConsole":24},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../bean/GlobalBean":18,"../net/MessageId":78,"../net/WebSocketManager":51,"../ToastController":49,"./HallAutoController":45,"./HallCreateRoomController":54,"./HallJoinRoomController":35},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../bean/GlobalBean":18,"../common/GameMgr":30,"../net/MessageId":78,"../net/WebSocketManager":51,"../net/WebSocketTool":62,"../ToastController":49,"../util/AudioManager":72,"./HallParentController":38,"./InfoDialogController":34,"./KickOutDialogController":36,"./LeaveDialogController":43,"./Level/LevelSelectPageController":46,"./MatchParentController":40,"./SettingDialogController":53},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../../bean/GlobalBean":18,"../../pfb/PlayerGameController ":64},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../net/MessageId":78,"../net/WebSocketManager":51,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../../net/WebSocketManager":51,"../../net/MessageId":78},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":18,"../common/GameMgr":30,"../net/MessageId":78,"../net/WebSocketManager":51,"../ToastController":49,"../util/Config":77,"../util/Tools":68,"./HallCenterLayController":31},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/PlayerScoreController":60},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":18,"../common/EventCenter":26,"../common/GameMgr":30,"../net/MessageBaseBean":55,"../pfb/MatchItemController":12,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../../net/WebSocketManager":51,"../../net/MessageId":78},"path":"preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../common/GameMgr":30,"../net/MessageId":78,"../net/WebSocketManager":51,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../common/GameMgr":30,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../bean/GlobalBean":18,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../../GlobalManagerController":75,"./LevelSelectController":6,"../../net/MessageId":78,"../../net/WebSocketManager":51},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../bean/GlobalBean":18,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":26,"../common/GameMgr":30,"./WebSocketTool":62},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/AudioManager":72,"../util/Config":77,"../util/LocalStorageManager":79,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/SeatItemController":61,"../util/Config":77,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"./HttpUtils":50,"./MessageBaseBean":55,"./GameServerUrl":11,"../../meshTools/MeshTools":70,"../common/GameMgr":30,"../common/EventCenter":26},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"../bean/GlobalBean":18,"../util/NickNameLabel":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../util/NickNameLabel":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"./MessageBaseBean":55,"./MessageId":78,"../util/Tools":68,"../../meshTools/Singleton":8,"../common/EventCenter":26,"../common/GameMgr":30},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/Config":77,"../util/NickNameLabel":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"./AudioManager":72,"./Config":77},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"../common/EventCenter":26,"../common/GameMgr":30,"../net/MessageBaseBean":55,"../util/Config":77},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"./tools/Publish":3},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":79},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../meshTools/MeshTools":70,"../meshTools/tools/Publish":3,"./bean/EnumBean":19,"./bean/GlobalBean":18,"./bean/LanguageType":20,"./common/EventCenter":26,"./common/GameMgr":30,"./game/GamePageController":27,"./hall/HallPageController":32,"./level/LevelPageController":10,"./hall/TopUpDialogController":44,"./net/ErrorCode":63,"./net/GameServerUrl":11,"./net/MessageBaseBean":55,"./net/MessageId":78,"./net/WebSocketManager":51,"./net/WebSocketTool":62,"./start_up/StartUpPageController":13,"./TipsDialogController":15,"./ToastController":49,"./util/AudioMgr":14,"./util/Config":77},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    