
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // AI托管显示节点
        _this.aiManagedNode = null; //AI托管显示节点
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        console.log("PlayerScoreController.start: \u7EC4\u4EF6\u542F\u52A8");
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
        // 不要默认隐藏AI托管节点，让setData方法来决定是否显示
        // this.hideAIManagedNode();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
            this.hideAIManagedNode();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
            // 设置AI托管状态显示
            if (user.isAIManaged !== undefined) {
                console.log("setData: \u8BBE\u7F6E\u73A9\u5BB6 " + user.nickName + " (" + user.userId + ") AI\u6258\u7BA1\u72B6\u6001: " + user.isAIManaged);
                this.setAIManagedStatus(user.isAIManaged);
            }
            else {
                // 如果没有AI托管状态信息，默认隐藏托管节点
                this.hideAIManagedNode();
            }
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    /**
     * 显示AI托管节点
     */
    PlayerScoreController.prototype.showAIManagedNode = function () {
        var _a;
        if (this.aiManagedNode) {
            this.aiManagedNode.active = true;
            // 详细调试信息
            console.log("AI\u6258\u7BA1\u8282\u70B9\u8C03\u8BD5\u4FE1\u606F:");
            console.log("- \u8282\u70B9\u540D\u79F0: " + this.aiManagedNode.name);
            console.log("- active\u72B6\u6001: " + this.aiManagedNode.active);
            console.log("- \u7236\u8282\u70B9\u5B58\u5728: " + !!this.aiManagedNode.parent);
            console.log("- \u7236\u8282\u70B9active: " + ((_a = this.aiManagedNode.parent) === null || _a === void 0 ? void 0 : _a.active));
            console.log("- \u8282\u70B9\u4F4D\u7F6E: x=" + this.aiManagedNode.x + ", y=" + this.aiManagedNode.y);
            console.log("- \u8282\u70B9\u5927\u5C0F: width=" + this.aiManagedNode.width + ", height=" + this.aiManagedNode.height);
            console.log("- \u8282\u70B9\u900F\u660E\u5EA6: " + this.aiManagedNode.opacity);
            console.log("- \u8282\u70B9\u7F29\u653E: scaleX=" + this.aiManagedNode.scaleX + ", scaleY=" + this.aiManagedNode.scaleY);
            // 检查父节点链
            var parent = this.aiManagedNode.parent;
            var level = 1;
            while (parent && level <= 3) {
                console.log("- \u7236\u8282\u70B9" + level + ": " + parent.name + ", active=" + parent.active + ", opacity=" + parent.opacity);
                parent = parent.parent;
                level++;
            }
            // 检查兄弟节点，看是否被遮挡
            if (this.aiManagedNode.parent) {
                var siblings = this.aiManagedNode.parent.children;
                var myIndex = siblings.indexOf(this.aiManagedNode);
                console.log("- \u8282\u70B9\u5728\u7236\u8282\u70B9\u4E2D\u7684\u7D22\u5F15: " + myIndex + "/" + siblings.length);
                console.log("- \u5144\u5F1F\u8282\u70B9\u5217\u8868:");
                siblings.forEach(function (sibling, index) {
                    console.log("  [" + index + "] " + sibling.name + ": active=" + sibling.active + ", zIndex=" + (sibling.zIndex || 0));
                });
            }
        }
    };
    /**
     * 隐藏AI托管节点
     */
    PlayerScoreController.prototype.hideAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = false;
        }
    };
    /**
     * 设置AI托管状态
     * @param isManaged 是否进入AI托管状态
     */
    PlayerScoreController.prototype.setAIManagedStatus = function (isManaged) {
        var _this = this;
        var _a, _b, _c, _d;
        console.log("PlayerScoreController.setAIManagedStatus: \u73A9\u5BB6 " + (((_a = this.currentUser) === null || _a === void 0 ? void 0 : _a.nickName) || '未知') + " (" + (((_b = this.currentUser) === null || _b === void 0 ? void 0 : _b.userId) || '未知') + ") \u8BBE\u7F6EAI\u6258\u7BA1\u72B6\u6001: " + isManaged);
        console.log("aiManagedNode \u5B58\u5728: " + !!this.aiManagedNode);
        if (isManaged) {
            this.showAIManagedNode();
            console.log("AI\u6258\u7BA1\u8282\u70B9\u5DF2\u663E\u793A\uFF0Cactive\u72B6\u6001: " + ((_c = this.aiManagedNode) === null || _c === void 0 ? void 0 : _c.active));
            // 强制刷新节点显示状态
            if (this.aiManagedNode) {
                this.scheduleOnce(function () {
                    console.log("\u5EF6\u8FDF\u68C0\u67E5AI\u6258\u7BA1\u8282\u70B9\u72B6\u6001: active=" + _this.aiManagedNode.active + ", \u53EF\u89C1=" + _this.aiManagedNode.activeInHierarchy);
                    // 检查Sprite组件
                    var sprite = _this.aiManagedNode.getComponent(cc.Sprite);
                    if (sprite) {
                        console.log("- Sprite\u7EC4\u4EF6\u5B58\u5728: enabled=" + sprite.enabled + ", spriteFrame=" + !!sprite.spriteFrame);
                    }
                    else {
                        console.log("- \u6CA1\u6709Sprite\u7EC4\u4EF6");
                    }
                    // 尝试强制刷新
                    _this.aiManagedNode.active = false;
                    _this.aiManagedNode.active = true;
                    // 再次检查状态
                    console.log("\u5F3A\u5236\u5237\u65B0\u540E: active=" + _this.aiManagedNode.active + ", \u53EF\u89C1=" + _this.aiManagedNode.activeInHierarchy);
                }, 0.1);
            }
        }
        else {
            this.hideAIManagedNode();
            console.log("AI\u6258\u7BA1\u8282\u70B9\u5DF2\u9690\u85CF\uFF0Cactive\u72B6\u6001: " + ((_d = this.aiManagedNode) === null || _d === void 0 ? void 0 : _d.active));
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "aiManagedNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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