
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fece9VdqbVCdoFnIuu8FUgl', 'PlayerScoreController');
// scripts/pfb/PlayerScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerScoreController = /** @class */ (function (_super) {
    __extends(PlayerScoreController, _super);
    function PlayerScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.nameLabel = null; //用户昵称
        // 分数显示相关节点
        _this.scoreBgMy = null; //我的分数背景节点 score_bg_my
        _this.scoreBgOthers = null; //其他人的分数背景节点 score_bg_others
        // 加减分效果节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        // AI托管显示节点
        _this.aiManagedNode = null; //AI托管显示节点
        // 当前用户数据
        _this.currentUser = null;
        return _this;
    }
    PlayerScoreController.prototype.start = function () {
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
        // 不要默认隐藏AI托管节点，让setData方法来决定是否显示
        // this.hideAIManagedNode();
    };
    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    PlayerScoreController.prototype.setData = function (user) {
        this.currentUser = user;
        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
            this.hideAIManagedNode();
        }
        else {
            // 设置头像和昵称
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            var nicknameLabel = this.nameLabel.getComponent(NickNameLabel_1.default);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;
            // 设置分数显示
            this.updateScore(user.score || 0);
            // 设置AI托管状态显示
            if (user.isAIManaged !== undefined) {
                this.setAIManagedStatus(user.isAIManaged);
            }
            else {
                // 如果没有AI托管状态信息，默认隐藏托管节点
                this.hideAIManagedNode();
            }
        }
    };
    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    PlayerScoreController.prototype.updateScore = function (score) {
        if (!this.currentUser)
            return;
        var isMyself = this.isCurrentUser(this.currentUser.userId);
        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        }
        else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    };
    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    PlayerScoreController.prototype.isCurrentUser = function (userId) {
        var _a, _b;
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        return userId === currentUserId;
    };
    /**
     * 显示我的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showMyScore = function (score) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            var myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                var labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    PlayerScoreController.prototype.showOthersScore = function (score) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            var otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                var labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    };
    /**
     * 隐藏所有分数背景
     */
    PlayerScoreController.prototype.hideAllScoreBackgrounds = function () {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    };
    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    PlayerScoreController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            this.addScoreNode.active = true;
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.addScoreNode) {
                    _this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerScoreController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            this.subScoreNode.active = true;
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    };
    /**
     * 隐藏加减分效果节点
     */
    PlayerScoreController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    /**
     * 显示AI托管节点
     */
    PlayerScoreController.prototype.showAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = true;
        }
    };
    /**
     * 隐藏AI托管节点
     */
    PlayerScoreController.prototype.hideAIManagedNode = function () {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = false;
        }
    };
    /**
     * 设置AI托管状态
     * @param isManaged 是否进入AI托管状态
     */
    PlayerScoreController.prototype.setAIManagedStatus = function (isManaged) {
        var _this = this;
        if (isManaged) {
            this.showAIManagedNode();
            // 强制刷新节点显示状态
            if (this.aiManagedNode) {
                this.scheduleOnce(function () {
                    // 尝试强制刷新
                    _this.aiManagedNode.active = false;
                    _this.aiManagedNode.active = true;
                    // 再次检查状态
                }, 0.1);
            }
        }
        else {
            this.hideAIManagedNode();
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Label)
    ], PlayerScoreController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgMy", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "scoreBgOthers", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "subScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerScoreController.prototype, "aiManagedNode", void 0);
    PlayerScoreController = __decorate([
        ccclass
    ], PlayerScoreController);
    return PlayerScoreController;
}(cc.Component));
exports.default = PlayerScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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