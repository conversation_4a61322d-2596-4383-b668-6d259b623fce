
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/CongratsDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f3b5eIOZ4tF8YT//S1WoI5Z', 'CongratsDialogController');
// scripts/game/CongratsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var CongratsItemController_1 = require("../pfb/CongratsItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//结算页面
var CongratsDialogController = /** @class */ (function (_super) {
    __extends(CongratsDialogController, _super);
    function CongratsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.contentLay = null;
        _this.backBtn = null;
        _this.congratsItem = null; //列表的 item
        _this.layoutNode = null; //存放列表的布局
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.backCallback = null; //隐藏弹窗的回调
        _this.seconds = 10; //倒计时 10 秒
        return _this;
    }
    CongratsDialogController.prototype.onLoad = function () {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    CongratsDialogController.prototype.onEnable = function () {
        this.updateCountdownLabel(this.seconds);
        //Tools.setCountDownTimeLabel(this.backBtn)
    };
    CongratsDialogController.prototype.start = function () {
        var _this = this;
        //backBtn 按钮点击事件
        Tools_1.Tools.greenButton(this.backBtn, function () {
            _this.hide(true);
        });
        // 设置倒计时标签的固定位置
        this.setFixedCountdownPosition();
        // 设置整个按钮的点击效果（包括按钮背景和倒计时标签的统一反馈）
        this.setupButtonWithCountdownEffect();
    };
    CongratsDialogController.prototype.show = function (noticeSettlement, backCallback) {
        var _this = this;
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 每次显示时重新设置倒计时标签的固定位置，确保位置正确
        this.setFixedCountdownPosition();
        this._setData(noticeSettlement);
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .call(function () {
            // 动画完成后再次设置位置，确保位置正确
            _this.setFixedCountdownPosition();
        })
            .start();
    };
    CongratsDialogController.prototype._setData = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            this.startCountdown(10); // 仍然启动倒计时
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) {
            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币
            if ('coin' in userList[index]) {
                // UserSettlement 类型，直接使用 coin 字段
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = userList[index].coin;
            }
            else if ('coinChg' in userList[index]) {
                // PlayerFinalResult 类型，使用 coinChg 字段更新金币
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin += userList[index].coinChg;
            }
        }
        this.layoutNode.removeAllChildren();
        var _loop_1 = function (i) {
            var item = cc.instantiate(this_1.congratsItem);
            var data = userList[i];
            this_1.layoutNode.addChild(item);
            setTimeout(function () {
                item.getComponent(CongratsItemController_1.default).createData(data, GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        };
        var this_1 = this;
        for (var i = 0; i < userList.length; ++i) {
            _loop_1(i);
        }
        this.startCountdown(10); //倒计时 10 秒
    };
    // bool 在隐藏的时候是否返回大厅
    CongratsDialogController.prototype.hide = function (bool) {
        var _this = this;
        if (bool === void 0) { bool = false; }
        if (this.backCallback) {
            this.backCallback();
        }
        GameMgr_1.GameMgr.Console.Log('隐藏结算页面');
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
            if (bool) {
                GlobalBean_1.GlobalBean.GetInstance().cleanData();
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                    'data': { 'type': 2 } //2是结算弹窗跳转的
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        })
            .start();
    };
    CongratsDialogController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    };
    CongratsDialogController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 在开始倒计时前再次确保位置正确
        this.setFixedCountdownPosition();
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                _this.hide(true);
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    };
    CongratsDialogController.prototype.updateCountdownLabel = function (seconds) {
        var _this = this;
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
            // 每次更新倒计时文本后，重新设置位置，确保位置始终正确
            this.scheduleOnce(function () {
                _this.setFixedCountdownPosition();
            }, 0.01);
        }
    };
    // 设置固定的倒计时位置，左括号始终对准back文字的右边
    CongratsDialogController.prototype.setFixedCountdownPosition = function () {
        if (!this.backBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - backBtn不存在");
            return;
        }
        // 重新获取节点，确保引用是最新的
        var btn = this.backBtn.getChildByName('button_label');
        var timeBtn = this.backBtn.getChildByName('buttonLabel_time');
        if (!btn || !timeBtn) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 缺少子节点", {
                hasBtn: !!btn,
                hasTimeBtn: !!timeBtn
            });
            return;
        }
        // 重新获取倒计时标签组件，确保引用正确
        this.countdownTimeLabel = timeBtn.getComponent(cc.Label);
        if (!this.countdownTimeLabel) {
            console.warn("CongratsDialogController: setFixedCountdownPosition - 无法获取Label组件");
            return;
        }
        // 延迟一帧执行，确保所有UI初始化完成
        this.scheduleOnce(function () {
            // 强制设置锚点为左对齐，确保左括号位置固定
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5; // 确保垂直居中
            // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置
            var fixedLeftPos = btn.position.x + btn.width / 2 - 10; // 5像素间距
            // 直接设置位置
            timeBtn.setPosition(fixedLeftPos, 0);
            // 再次强制设置锚点，防止被其他代码重置
            timeBtn.anchorX = 0;
            timeBtn.anchorY = 0.5;
        }, 0.01);
    };
    // 设置整个按钮的统一点击效果（按钮背景和倒计时标签都有反馈）
    CongratsDialogController.prototype.setupButtonWithCountdownEffect = function () {
        var _this = this;
        if (!this.backBtn) {
            console.warn("CongratsDialogController: backBtn未设置，无法添加点击效果");
            return;
        }
        // 获取按钮的子节点
        var btnColorNormal = this.backBtn.getChildByName('btn_color_normal');
        var buttonLabel = this.backBtn.getChildByName('button_label');
        if (!btnColorNormal || !buttonLabel) {
            console.warn("CongratsDialogController: 按钮结构不完整，无法添加点击效果");
            return;
        }
        var label = buttonLabel.getComponent(cc.Label);
        var labelOutline = buttonLabel.getComponent(cc.LabelOutline);
        if (!label || !labelOutline) {
            console.warn("CongratsDialogController: 按钮标签组件不完整");
            return;
        }
        // 记录倒计时标签的原始字体大小
        var originalCountdownFontSize = 36;
        var originalCountdownLineHeight = 36;
        if (this.countdownTimeLabel) {
            originalCountdownFontSize = this.countdownTimeLabel.fontSize;
            originalCountdownLineHeight = this.countdownTimeLabel.lineHeight;
        }
        // 自定义按钮点击效果，同时控制倒计时标签
        Tools_1.Tools.setTouchEvent(btnColorNormal, 
        // 按下时：按钮和倒计时标签都变暗
        function (node) {
            // 按钮背景变暗
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenPressed);
            // 按钮文字变暗
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenPressedColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            // 倒计时标签也变暗（字体缩小效果）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小缩小（从原始大小缩小2）
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize - 2;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight - 2;
                // 轮廓颜色变化（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenPressedColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色变暗
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
            }
        }, 
        // 抬起时：恢复正常并执行点击逻辑
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
            // 执行点击逻辑
            _this.hide(true);
        }, 
        // 取消时：恢复正常
        function (node) {
            // 按钮背景恢复
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.btnGreenNormal);
            // 按钮文字恢复
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, Config_1.Config.btnGreenNormalColor);
            labelOutline.color = color;
            buttonLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            // 倒计时标签恢复（恢复到原始大小）
            if (_this.countdownTimeLabel && _this.countdownTimeLabel.node) {
                // 字体大小恢复到原始大小
                _this.countdownTimeLabel.fontSize = originalCountdownFontSize;
                _this.countdownTimeLabel.lineHeight = originalCountdownLineHeight;
                // 轮廓颜色恢复（如果有LabelOutline组件）
                var countdownOutline = _this.countdownTimeLabel.getComponent(cc.LabelOutline);
                if (countdownOutline) {
                    var outlineColor = new cc.Color();
                    cc.Color.fromHEX(outlineColor, Config_1.Config.btnGreenNormalColor);
                    countdownOutline.color = outlineColor;
                }
                // 文字颜色恢复
                _this.countdownTimeLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            }
        });
    };
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Prefab)
    ], CongratsDialogController.prototype, "congratsItem", void 0);
    __decorate([
        property(cc.Node)
    ], CongratsDialogController.prototype, "layoutNode", void 0);
    CongratsDialogController = __decorate([
        ccclass
    ], CongratsDialogController);
    return CongratsDialogController;
}(cc.Component));
exports.default = CongratsDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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