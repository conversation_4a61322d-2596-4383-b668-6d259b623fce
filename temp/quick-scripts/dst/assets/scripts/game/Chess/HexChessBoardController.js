
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        // 首先检查是否是非格子节点（预制体等），如果是则直接返回null，不报错
        if (this.isNonGridNode(nodeName)) {
            return null;
        }
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        // 只有当节点名称看起来像格子节点但无法解析时才报错
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 判断是否是非格子节点（预制体等）
    HexChessBoardController.prototype.isNonGridNode = function (nodeName) {
        // 炸弹预制体
        if (nodeName === "HexBoom" || nodeName === "Boom") {
            return true;
        }
        // 数字预制体
        if (nodeName.match(/^(Hex)?Boom\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar") ||
            nodeName === "player_game_pfb") {
            return true;
        }
        // 临时数字节点
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // UI相关节点
        if (nodeName.includes("UI") || nodeName.includes("ui") ||
            nodeName.includes("Score") || nodeName.includes("score")) {
            return true;
        }
        return false;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y - 12); // 改为往左偏移10像素
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 检查游戏状态：只有在扫雷进行中(gameStatus=1)时才能生成头像预制体
        var gamePageController = cc.find("Canvas").getComponent("GamePageController");
        if (gamePageController && gamePageController.gameStatus !== 1) {
            console.warn("\u274C placePlayerOnHexGrid: \u6E38\u620F\u72B6\u6001\u4E3A" + gamePageController.gameStatus + "\uFF0C\u4E0D\u5141\u8BB8\u751F\u6210\u5934\u50CF\u9884\u5236\u4F53\uFF08\u53EA\u6709\u72B6\u6001=1\u65F6\u5141\u8BB8\uFF09");
            return;
        }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 设置单人放置的缩放为0.8
        playerNode.setScale(0.8);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法清除游戏元素");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("sixblock_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体（包括方形地图的Boom和六边形地图的HexBoom）
        if (nodeName === "Boom" || nodeName === "HexBoom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等，以及 HexBoom1, HexBoom2 等）
        if (nodeName.match(/^(Hex)?Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法显示六边形格子");
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点（sixblock_q_r 格式）
            if (child.name.startsWith("sixblock_") || child.name === "hexblock") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
        if (totalGrids === 0) {
            console.warn("⚠️ 没有找到任何六边形格子节点！请检查节点命名是否正确");
            // 列出所有子节点名称以便调试
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放六边形格子消失动画
                this.playHexGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放六边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    HexChessBoardController.prototype.playHexGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        console.log("🔄 重新初始化六边形棋盘数据，清理所有玩家状态");
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData, key) {
            // 如果还有玩家节点引用，先移除它
            if (gridData.playerNode) {
                console.log("\uD83D\uDDD1\uFE0F \u6E05\u7406\u516D\u8FB9\u5F62\u683C\u5B50" + key + "\u7684\u73A9\u5BB6\u8282\u70B9\u5F15\u7528");
                if (gridData.playerNode.isValid) {
                    gridData.playerNode.removeFromParent();
                }
                gridData.playerNode = null;
            }
            // 重置玩家状态
            gridData.hasPlayer = false;
        });
        console.log("✅ 六边形棋盘数据重新初始化完成");
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）- 已废弃，不再支持断线重连生成头像
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        console.log("batchPlaceHexPlayers已废弃：断线重连不再生成头像预制体");
        // 不再执行任何操作
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
        });
        // 测试一些中间坐标
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有六边形格子
        this.shakeAllHexGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    HexChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有六边形格子
     */
    HexChessBoardController.prototype.shakeAllHexGrids = function (intensity, duration, frequency) {
        var _this = this;
        if (!this.hexGridNodes)
            return;
        // 遍历所有六边形格子节点
        this.hexGridNodes.forEach(function (gridNode, key) {
            if (!gridNode || !gridNode.active)
                return;
            // 为每个格子创建独立的震动动画
            _this.shakeHexGridNode(gridNode, intensity, duration, frequency);
        });
    };
    /**
     * 震动单个六边形格子节点
     */
    HexChessBoardController.prototype.shakeHexGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        var foundPlayer = false;
        this.hexGridData.forEach(function (gridData) {
            // 如果已经找到了，就不再继续查找
            if (foundPlayer) {
                return;
            }
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    _this.showScoreOnHexPlayerController(playerController, score);
                    foundPlayer = true;
                }
            }
        });
        if (!foundPlayer) {
            console.warn("❌ 未找到当前用户的六边形头像节点");
        }
        return foundPlayer;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureHexScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保六边形加分/减分节点在最高层级
     */
    HexChessBoardController.prototype.ensureHexScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        // 检查游戏状态：断线重连时不生成任何头像预制体
        var gamePageController = cc.find("Canvas").getComponent("GamePageController");
        if (gamePageController && gamePageController.gameStatus !== 1) {
            console.log("\u274C displayOtherPlayersAtHexPosition: \u6E38\u620F\u72B6\u6001\u4E3A" + gamePageController.gameStatus + "\uFF0C\u65AD\u7EBF\u91CD\u8FDE\u65F6\u4E0D\u751F\u6210\u5176\u4ED6\u73A9\u5BB6\u5934\u50CF\u9884\u5236\u4F53");
            return;
        }
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getHexPlayerPositions(4);
        }
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩放动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    HexChessBoardController.prototype.calculateHexBasePositionByPlayerCount = function (q, r, totalPlayers) {
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）
            return this.getHexWorldPosition(q, r, true);
        }
        else {
            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）
            return this.getHexWorldPosition(q, r, false);
        }
    };
    /**
     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode || !playerNode.isValid) {
            return;
        }
        // 停止之前的动画
        playerNode.stopAllActions();
        // 使用cc.tween播放位置和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        // 根据总人数设置缩放：单人0.8，多人使用position.scale
        if (totalPlayers === 1) {
            playerNode.setScale(0.8);
        }
        else {
            playerNode.setScale(position.scale);
        }
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    HexChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        // 检查数据格式
        if (Array.isArray(mapData) && mapData.length > 0) {
            if (Array.isArray(mapData[0])) {
                // 二维数组格式：mapData[x][y] 表示每个格子的状态
                this.restoreFromGridArray(mapData);
            }
            else if (mapData[0] && typeof mapData[0] === 'object' && ('q' in mapData[0] || 'x' in mapData[0])) {
                // 联机模式格式：[{q, r, isRevealed, neighborMines, ...}, ...]
                this.restoreFromOnlineArray(mapData);
            }
            else {
                console.warn("未知的数组格式:", mapData[0]);
            }
        }
        else if (mapData && typeof mapData === 'object' && ('revealedBlocks' in mapData || 'markedBlocks' in mapData)) {
            // 对象格式：{revealedBlocks: [...], markedBlocks: [...]}
            this.restoreFromBlockLists(mapData);
        }
        else {
            console.warn("无效的mapData格式:", mapData);
        }
    };
    /**
     * 从二维数组格式恢复地图状态
     * @param mapData 二维数组格式的地图数据
     */
    HexChessBoardController.prototype.restoreFromGridArray = function (mapData) {
        var _this = this;
        for (var x = 0; x < mapData.length; x++) {
            var _loop_2 = function (y) {
                var gridInfo = mapData[x][y];
                if (gridInfo && gridInfo.isRevealed) {
                    // 六边形地图中，x对应q坐标，y对应r坐标
                    var q_1 = x;
                    var r_1 = y;
                    if (this_2.isValidHexCoordinate(q_1, r_1)) {
                        // 立即隐藏格子（不播放动画）
                        this_2.hideHexGridAt(q_1, r_1, true);
                        // 显示挖掘结果
                        var neighborMines_1 = gridInfo.neighborMines || 0;
                        if (neighborMines_1 > 0) {
                            // 延迟创建数字预制体，确保格子先隐藏
                            this_2.scheduleOnce(function () {
                                _this.createHexNumberPrefab(q_1, r_1, neighborMines_1);
                            }, 0.05);
                        }
                    }
                }
                if (gridInfo && gridInfo.isMarked) {
                    var q_2 = x;
                    var r_2 = y;
                    if (this_2.isValidHexCoordinate(q_2, r_2)) {
                        // 延迟创建标记预制体
                        this_2.scheduleOnce(function () {
                            _this.createHexBiaojiPrefab(q_2, r_2);
                        }, 0.1);
                    }
                }
            };
            var this_2 = this;
            for (var y = 0; y < mapData[x].length; y++) {
                _loop_2(y);
            }
        }
    };
    /**
     * 从联机模式数组格式恢复地图状态
     * @param mapData 联机模式数组格式的地图数据
     */
    HexChessBoardController.prototype.restoreFromOnlineArray = function (mapData) {
        var _this = this;
        mapData.forEach(function (gridInfo) {
            // 六边形地图使用q和r坐标，如果没有则使用x和y
            var q = gridInfo.q !== undefined ? gridInfo.q : gridInfo.x;
            var r = gridInfo.r !== undefined ? gridInfo.r : gridInfo.y;
            if (_this.isValidHexCoordinate(q, r)) {
                // 优先处理已挖掘状态，因为已挖掘的格子应该显示挖掘结果，而不是标记
                if (gridInfo.isRevealed) {
                    // 兼容不同的字段名：neighborMines（小写）或 NeighborMines（大写）
                    var neighborMines_2 = gridInfo.neighborMines !== undefined ? gridInfo.neighborMines : (gridInfo.NeighborMines || 0);
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u516D\u8FB9\u5F62\u65B9\u5757: (" + q + ", " + r + "), \u662F\u5426\u5730\u96F7: " + gridInfo.isMine + ", \u5468\u56F4\u5730\u96F7\u6570: " + neighborMines_2 + ", \u662F\u5426\u6807\u8BB0: " + gridInfo.isMarked);
                    // 立即隐藏格子（不播放动画）
                    _this.hideHexGridAt(q, r, true);
                    // 显示挖掘结果
                    if (gridInfo.isMine) {
                        // 这是一个已挖掘的地雷，创建炸弹预制体
                        _this.scheduleOnce(function () {
                            console.log("\u521B\u5EFA\u516D\u8FB9\u5F62\u70B8\u5F39\u9884\u5236\u4F53: (" + q + ", " + r + ")");
                            _this.createHexBoomPrefab(q, r, false); // 断线重连时不是当前用户点击的
                        }, 0.05);
                    }
                    else if (neighborMines_2 > 0) {
                        // 这是一个普通格子，显示数字
                        _this.scheduleOnce(function () {
                            console.log("\u521B\u5EFA\u516D\u8FB9\u5F62\u6570\u5B57\u9884\u5236\u4F53: (" + q + ", " + r + "), \u6570\u5B57: " + neighborMines_2);
                            _this.createHexNumberPrefab(q, r, neighborMines_2);
                        }, 0.05);
                    }
                    // 如果是已挖掘格子，不再处理标记状态（已挖掘的格子不应该显示标记）
                    return;
                }
                // 只有未挖掘的格子才处理标记状态
                if (gridInfo.isMarked) {
                    console.log("\u516D\u8FB9\u5F62\u8054\u673A\u6A21\u5F0F\u68C0\u6D4B\u5230\u6807\u8BB0\u6570\u636E: (" + q + ", " + r + ")\uFF0C\u57FA\u4E8E\u73A9\u5BB6\u5171\u8BC6\u751F\u6210\u6807\u8BB0\u9884\u5236\u4F53");
                    // 标记的格子需要隐藏原始格子
                    _this.hideHexGridAt(q, r, true);
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createHexBiaojiPrefab(q, r);
                    }, 0.1);
                }
                // 断线重连时不生成玩家头像预制体
                if (gridInfo.players && Array.isArray(gridInfo.players) && gridInfo.players.length > 0) {
                    // 记录玩家位置信息但不创建头像预制体
                }
            }
        });
    };
    /**
     * 从对象格式恢复地图状态
     * @param mapData 包含revealedBlocks和markedBlocks的对象
     */
    HexChessBoardController.prototype.restoreFromBlockLists = function (mapData) {
        var _this = this;
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            mapData.revealedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidHexCoordinate(q, r)) {
                    // 立即隐藏格子（不播放动画）
                    _this.hideHexGridAt(q, r, true);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createHexNumberPrefab(q, r, neighborMines);
                        }, 0.05);
                    }
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            mapData.markedBlocks.forEach(function (block) {
                // 六边形地图使用q和r坐标，如果没有则使用x和y
                var q = block.q !== undefined ? block.q : block.x;
                var r = block.r !== undefined ? block.r : block.y;
                if (_this.isValidHexCoordinate(q, r)) {
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createHexBiaojiPrefab(q, r);
                    }, 0.1);
                }
            });
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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