
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/ChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 检查游戏状态：只有在扫雷进行中(gameStatus=1)时才能生成头像预制体
        var gamePageController = cc.find("Canvas").getComponent("GamePageController");
        if (gamePageController && gamePageController.gameStatus !== 1) {
            console.warn("\u274C placePlayerOnGrid: \u6E38\u620F\u72B6\u6001\u4E3A" + gamePageController.gameStatus + "\uFF0C\u4E0D\u5141\u8BB8\u751F\u6210\u5934\u50CF\u9884\u5236\u4F53\uFF08\u53EA\u6709\u72B6\u6001=1\u65F6\u5141\u8BB8\uFF09");
            return;
        }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）- 已废弃，不再支持断线重连生成头像
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        console.log("batchPlacePlayers已废弃：断线重连不再生成头像预制体");
        // 不再执行任何操作
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法（测试方法已废弃）
        console.log("测试方法已废弃：不再支持跳过游戏状态检查生成头像");
        // this.placePlayerOnGrid(x, y, false);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保加分/减分节点在最高层级
     */
    ChessBoardController.prototype.ensureScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    ChessBoardController.prototype.testReset = function () {
        this.resetGameScene();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
            else {
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            child.removeFromParent();
        });
        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有游戏预制体
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            var shouldKeep = nodeName.startsWith("Grid_") ||
                nodeName.includes("Score") ||
                nodeName.includes("score") ||
                nodeName.includes("UI") ||
                nodeName.includes("ui") ||
                nodeName.includes("Canvas") ||
                nodeName.includes("Background");
            if (!shouldKeep) {
                childrenToRemove.push(child);
            }
            // 移除找到的节点
            childrenToRemove.forEach(function (child) {
                child.removeFromParent();
            });
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
            return false;
        }
        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 🗑️明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        //  默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 使用动画隐藏格子
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(function () {
                    gridNode.active = false;
                })
                    .start();
            }
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        // 检查游戏状态：断线重连时不生成任何头像预制体
        var gamePageController = cc.find("Canvas").getComponent("GamePageController");
        if (gamePageController && gamePageController.gameStatus !== 1) {
            console.log("\u274C displayOtherPlayersAtPosition: \u6E38\u620F\u72B6\u6001\u4E3A" + gamePageController.gameStatus + "\uFF0C\u65AD\u7EBF\u91CD\u8FDE\u65F6\u4E0D\u751F\u6210\u5176\u4ED6\u73A9\u5BB6\u5934\u50CF\u9884\u5236\u4F53");
            return;
        }
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    ChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    ChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    ChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    ChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    /**
     * 恢复联机模式地图状态（断线重连时使用）
     * @param mapData 地图数据
     */
    ChessBoardController.prototype.restoreOnlineMapState = function (mapData) {
        // 检查数据格式
        if (Array.isArray(mapData) && mapData.length > 0) {
            if (Array.isArray(mapData[0])) {
                // 二维数组格式：mapData[x][y] 表示每个格子的状态
                this.restoreFromGridArray(mapData);
            }
            else if (mapData[0] && typeof mapData[0] === 'object' && ('x' in mapData[0] && 'y' in mapData[0])) {
                // 联机模式格式：[{x, y, isRevealed, neighborMines, ...}, ...]
                this.restoreFromOnlineArray(mapData);
            }
            else {
                console.warn("未知的数组格式:", mapData[0]);
            }
        }
        else if (mapData && typeof mapData === 'object' && ('revealedBlocks' in mapData || 'markedBlocks' in mapData)) {
            // 对象格式：{revealedBlocks: [...], markedBlocks: [...]}
            this.restoreFromBlockLists(mapData);
        }
        else {
            console.warn("无效的mapData格式:", mapData);
        }
    };
    /**
     * 从二维数组格式恢复地图状态
     * @param mapData 二维数组格式的地图数据
     */
    ChessBoardController.prototype.restoreFromGridArray = function (mapData) {
        // 直接遍历二维数组，但始终使用数据中的坐标，而不是数组索引
        // 因为服务端的数组结构可能与前端的坐标系统不一致
        for (var i = 0; i < mapData.length; i++) {
            for (var j = 0; j < mapData[i].length; j++) {
                var gridInfo = mapData[i][j];
                if (gridInfo && gridInfo.x !== undefined && gridInfo.y !== undefined) {
                    // 始终使用数据中的坐标
                    var actualX = gridInfo.x;
                    var actualY = gridInfo.y;
                    if (this.isValidCoordinate(actualX, actualY)) {
                        this.processGridRestore(actualX, actualY, gridInfo);
                    }
                    else {
                        console.warn("\u65E0\u6548\u5750\u6807: (" + actualX + ", " + actualY + ")");
                    }
                }
                else if (gridInfo) {
                    console.warn("\u683C\u5B50\u6570\u636E\u7F3A\u5C11\u5750\u6807\u4FE1\u606F:", gridInfo);
                }
            }
        }
    };
    /**
     * 处理单个格子的恢复
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param gridInfo 格子信息
     */
    ChessBoardController.prototype.processGridRestore = function (x, y, gridInfo) {
        var _this = this;
        // 优先处理已挖掘状态，因为已挖掘的格子应该显示挖掘结果，而不是标记
        if (gridInfo.isRevealed) {
            console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u662F\u5426\u5730\u96F7: " + gridInfo.isMine + ", \u5468\u56F4\u5730\u96F7\u6570: " + (gridInfo.neighborMines || 0) + ", \u662F\u5426\u6807\u8BB0: " + gridInfo.isMarked);
            // 立即隐藏格子（不播放动画）
            this.hideGridAt(x, y, true);
            // 显示挖掘结果
            if (gridInfo.isMine) {
                // 这是一个已挖掘的地雷，创建炸弹预制体
                this.scheduleOnce(function () {
                    console.log("\u521B\u5EFA\u70B8\u5F39\u9884\u5236\u4F53: (" + x + ", " + y + ")");
                    _this.createBoomPrefab(x, y, false); // 断线重连时不是当前用户点击的
                }, 0.05);
            }
            else {
                // 这是一个普通格子，显示数字
                var neighborMines_1 = gridInfo.neighborMines || 0;
                if (neighborMines_1 > 0) {
                    // 延迟创建数字预制体，确保格子先隐藏
                    this.scheduleOnce(function () {
                        console.log("\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53: (" + x + ", " + y + "), \u6570\u5B57: " + neighborMines_1);
                        _this.createNumberPrefab(x, y, neighborMines_1);
                    }, 0.05);
                }
            }
            // 如果是已挖掘格子，不再处理标记状态（已挖掘的格子不应该显示标记）
            return;
        }
        // 只有未挖掘的格子才处理标记状态
        if (gridInfo.isMarked) {
            console.log("\u8054\u673A\u6A21\u5F0F\u68C0\u6D4B\u5230\u6807\u8BB0\u6570\u636E: (" + x + ", " + y + ")\uFF0C\u57FA\u4E8E\u73A9\u5BB6\u5171\u8BC6\u751F\u6210\u6807\u8BB0\u9884\u5236\u4F53");
            // 标记的格子需要隐藏原始格子
            this.hideGridAt(x, y, true);
            // 延迟创建标记预制体
            this.scheduleOnce(function () {
                _this.createBiaojiPrefab(x, y);
            }, 0.1);
        }
        // 断线重连时不生成玩家头像预制体
        if (gridInfo.players && Array.isArray(gridInfo.players) && gridInfo.players.length > 0) {
            // 记录玩家位置信息但不创建头像预制体
        }
    };
    /**
     * 从联机模式数组格式恢复地图状态
     * @param mapData 联机模式数组格式的地图数据
     */
    ChessBoardController.prototype.restoreFromOnlineArray = function (mapData) {
        var _this = this;
        mapData.forEach(function (gridInfo) {
            var x = gridInfo.x;
            var y = gridInfo.y;
            if (_this.isValidCoordinate(x, y)) {
                // 优先处理已挖掘状态，因为已挖掘的格子应该显示挖掘结果，而不是标记
                if (gridInfo.isRevealed) {
                    console.log("\u6062\u590D\u5DF2\u6316\u6398\u65B9\u5757: (" + x + ", " + y + "), \u662F\u5426\u5730\u96F7: " + gridInfo.isMine + ", \u5468\u56F4\u5730\u96F7\u6570: " + (gridInfo.neighborMines || gridInfo.NeighborMines || 0) + ", \u662F\u5426\u6807\u8BB0: " + gridInfo.isMarked);
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y, true);
                    // 显示挖掘结果
                    // 兼容不同的字段名：neighborMines（小写）或 NeighborMines（大写）
                    var neighborMines_2 = gridInfo.neighborMines !== undefined ? gridInfo.neighborMines : (gridInfo.NeighborMines || 0);
                    if (gridInfo.isMine) {
                        // 这是一个已挖掘的地雷，创建炸弹预制体
                        _this.scheduleOnce(function () {
                            console.log("\u521B\u5EFA\u70B8\u5F39\u9884\u5236\u4F53: (" + x + ", " + y + ")");
                            _this.createBoomPrefab(x, y, false); // 断线重连时不是当前用户点击的
                        }, 0.05);
                    }
                    else if (neighborMines_2 > 0) {
                        // 这是一个普通格子，显示数字
                        _this.scheduleOnce(function () {
                            console.log("\u521B\u5EFA\u6570\u5B57\u9884\u5236\u4F53: (" + x + ", " + y + "), \u6570\u5B57: " + neighborMines_2);
                            _this.createNumberPrefab(x, y, neighborMines_2);
                        }, 0.05);
                    }
                    // 如果是已挖掘格子，不再处理标记状态（已挖掘的格子不应该显示标记）
                    return;
                }
                // 只有未挖掘的格子才处理标记状态
                if (gridInfo.isMarked) {
                    console.log("\u8054\u673A\u6A21\u5F0F\u68C0\u6D4B\u5230\u6807\u8BB0\u6570\u636E: (" + x + ", " + y + ")\uFF0C\u57FA\u4E8E\u73A9\u5BB6\u5171\u8BC6\u751F\u6210\u6807\u8BB0\u9884\u5236\u4F53");
                    // 标记的格子需要隐藏原始格子
                    _this.hideGridAt(x, y, true);
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createBiaojiPrefab(x, y);
                    }, 0.1);
                }
            }
        });
    };
    /**
     * 从对象格式恢复地图状态
     * @param mapData 包含revealedBlocks和markedBlocks的对象
     */
    ChessBoardController.prototype.restoreFromBlockLists = function (mapData) {
        var _this = this;
        // 恢复已挖掘的方块
        if (mapData.revealedBlocks && Array.isArray(mapData.revealedBlocks)) {
            mapData.revealedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                var neighborMines = block.neighborMines;
                if (_this.isValidCoordinate(x, y)) {
                    // 立即隐藏格子（不播放动画）
                    _this.hideGridAt(x, y, true);
                    // 显示挖掘结果
                    if (neighborMines > 0) {
                        // 延迟创建数字预制体，确保格子先隐藏
                        _this.scheduleOnce(function () {
                            _this.createNumberPrefab(x, y, neighborMines);
                        }, 0.05);
                    }
                }
            });
        }
        // 恢复已标记的方块
        if (mapData.markedBlocks && Array.isArray(mapData.markedBlocks)) {
            mapData.markedBlocks.forEach(function (block) {
                var x = block.x;
                var y = block.y;
                if (_this.isValidCoordinate(x, y)) {
                    // 延迟创建标记预制体
                    _this.scheduleOnce(function () {
                        _this.createBiaojiPrefab(x, y);
                    }, 0.1);
                }
            });
        }
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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