
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GameScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef3738C6EtNP63rWdv6bSAj', 'GameScoreController');
// scripts/game/GameScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var PlayerScoreController_1 = require("../pfb/PlayerScoreController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GameScoreController = /** @class */ (function (_super) {
    __extends(GameScoreController, _super);
    function GameScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scoreLayout = null; // 分数布局容器
        _this.playerScorePfb = null; // player_score_pfb 预制体
        _this._scoreControllers = []; // 分数控制器数组
        return _this;
        /**
         * 更新player_game_pfb中的change_score显示
         * @param userId 用户ID
         * @param bonusScore 奖励分数
         */
        // update (dt) {}
    }
    // onLoad () {}
    GameScoreController.prototype.onLoad = function () {
        // 初始化时不自动创建界面，等待游戏数据
    };
    GameScoreController.prototype.start = function () {
        // 不在start中自动创建，等待外部调用
    };
    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    GameScoreController.prototype.createScoreView = function () {
        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }
        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }
        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }
        // 获取后端传回来的用户数据
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 确保所有用户都有score字段，初始化为0
        users.forEach(function (user, index) {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
        });
        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];
        // 根据后端用户数据生成分数预制体
        for (var i = 0; i < users.length; i++) {
            var item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);
            var scoreController = item.getComponent(PlayerScoreController_1.default);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
            }
            else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }
    };
    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    GameScoreController.prototype.initializeScoreView = function () {
        this.createScoreView();
    };
    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    GameScoreController.prototype.setGameData = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 更新所有玩家的分数显示
        for (var i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
            }
        }
    };
    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    GameScoreController.prototype.updatePlayerScore = function (userId, score) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6\u6216\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex);
        }
    };
    /**
     * 更新所有玩家分数
     */
    GameScoreController.prototype.updateAllScores = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        for (var i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
        }
    };
    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    GameScoreController.prototype.getPlayerScoreController = function (userIndex) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GameScoreController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === data.userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isMyself = (data.userId === currentUserId);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + data.userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
            // 打印所有用户信息用于调试
            users.forEach(function (user, index) {
            });
        }
    };
    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.showAddScoreWithAnimation = function (userIndex, bonusScore) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            var scoreController = this._scoreControllers[userIndex];
            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);
        }
        else {
            console.warn("\u65E0\u6548\u7684\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u63A7\u5236\u5668\u6570\u91CF: " + this._scoreControllers.length);
        }
    };
    /**
     * 处理AI托管状态变更
     * @param userId 用户ID
     * @param isAIManaged 是否进入AI托管状态
     */
    GameScoreController.prototype.onAIStatusChange = function (userId, isAIManaged) {
        console.log("GameScoreController.onAIStatusChange: userId=" + userId + ", isAIManaged=" + isAIManaged);
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理AI托管状态变更");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        console.log("\u67E5\u627E\u73A9\u5BB6\u7D22\u5F15: userId=" + userId + ", userIndex=" + userIndex + ", \u603B\u7528\u6237\u6570=" + users.length + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 设置对应玩家的AI托管状态显示
            console.log("\u8C03\u7528 _scoreControllers[" + userIndex + "].setAIManagedStatus(" + isAIManaged + ")");
            this._scoreControllers[userIndex].setAIManagedStatus(isAIManaged);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
        }
    };
    __decorate([
        property(cc.Node)
    ], GameScoreController.prototype, "scoreLayout", void 0);
    __decorate([
        property(cc.Prefab)
    ], GameScoreController.prototype, "playerScorePfb", void 0);
    GameScoreController = __decorate([
        ccclass
    ], GameScoreController);
    return GameScoreController;
}(cc.Component));
exports.default = GameScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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