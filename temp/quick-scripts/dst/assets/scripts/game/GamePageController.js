
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var PlayerGameController_1 = require("../pfb/PlayerGameController ");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var AIManagedDialogController_1 = require("./AIManagedDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.aiManagedDialogController = null; // AI托管页面
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.gameStartNode = null; // 游戏开始节点
        _this.roundStartNode = null; // 回合开始节点
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态（公开属性，供棋盘控制器访问）
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        _this.isCurrentUserAIManaged = false; // 当前用户是否处于AI托管状态
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        // 当前NoticeActionDisplay数据，用于倒计时显示逻辑
        _this.currentNoticeActionData = null;
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
        // 初始化游戏开始和回合开始节点
        this.initializeAnimationNodes();
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
        // 为整个游戏页面添加点击事件监听，用于处理托管状态下的取消操作
        this.node.on(cc.Node.EventType.TOUCH_END, this.onGamePageClick, this);
    };
    /**
     * 处理游戏页面点击事件（用于托管状态下的取消操作）
     * @param event 点击事件
     */
    GamePageController.prototype.onGamePageClick = function (event) {
        // 只有在托管状态下才处理
        if (this.isCurrentUserAIManaged) {
            this.sendCancelAIManagement();
        }
    };
    /**
     * 发送取消AI托管消息
     */
    GamePageController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体（正常游戏流程，不跳过游戏状态检查）
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体（正常游戏流程，不跳过游戏状态检查）
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true, false);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
        // 停止所有动画并重置动画节点状态
        this.stopAllAnimations();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        var _this = this;
        var _a, _b, _c, _d;
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
                // 如果是联机模式断线重连且有地图数据，恢复棋盘状态
                if (data.mapData) {
                    this.scheduleOnce(function () {
                        _this.chessBoardController.restoreOnlineMapState(data.mapData);
                    }, 0.1);
                }
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
                // 如果是联机模式断线重连且有地图数据，恢复棋盘状态
                if (data.mapData) {
                    this.scheduleOnce(function () {
                        _this.hexChessBoardController.restoreOnlineMapState(data.mapData);
                    }, 0.1);
                }
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.gameStatus = data.gameStatus || 0;
        this.isCurrentUserAIManaged = false; // 重置托管状态
        // 根据gameStatus设置是否可以操作
        // 根据API.md定义的统一游戏状态值：
        // 0 = 等待开始, 1 = 扫雷进行中, 2 = 回合结束展示, 3 = 游戏结束, 4 = 关卡胜利, 5 = 关卡失败
        if (data.gameStatus === 1) {
            // 扫雷进行中，玩家可以操作
            this.canOperate = true;
        }
        else {
            // 其他状态（等待开始、展示阶段、游戏结束等），玩家不能操作
            this.canOperate = false;
        }
        // 处理AI托管状态（仅联机模式）
        // 使用类型断言来访问可能存在的isAIManaged字段
        var dataWithAI = data;
        if (dataWithAI.isAIManaged !== undefined) {
            this.isCurrentUserAIManaged = dataWithAI.isAIManaged;
            if (dataWithAI.isAIManaged) {
                // 延迟显示托管UI，确保界面初始化完成
                this.scheduleOnce(function () {
                    _this.showAIManagedDialog();
                }, 0.3);
                // 同时更新GameScoreController中的托管状态显示
                var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
                if (this.gameScoreController && currentUserId) {
                    this.gameScoreController.onAIStatusChange(currentUserId, true);
                }
            }
            else {
                this.hideAIManagedDialog();
                // 同时更新GameScoreController中的托管状态显示
                var currentUserId = (_d = (_c = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _c === void 0 ? void 0 : _c.userInfo) === null || _d === void 0 ? void 0 : _d.userId;
                if (this.gameScoreController && currentUserId) {
                    this.gameScoreController.onAIStatusChange(currentUserId, false);
                }
            }
        }
        else {
            // 没有isAIManaged字段，说明不在托管状态
            this.isCurrentUserAIManaged = false;
            this.hideAIManagedDialog();
        }
        // 处理倒计时（断线重连时使用服务端返回的countDown）
        if (data.countDown !== undefined && data.countDown > 0) {
            this.currentCountdown = data.countDown;
            this.updateCountdownDisplay(this.currentCountdown);
            // 延迟启动倒计时，确保所有初始化完成
            this.scheduleOnce(function () {
                _this.startCountdown(_this.currentCountdown);
            }, 0.2);
        }
        else {
            // 新游戏或没有倒计时信息
            this.currentCountdown = 0;
        }
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
            // 断线重连时更新玩家分数和AI托管状态
            var isReconnect_1 = data.mapData && (Array.isArray(data.mapData) || typeof data.mapData === 'object');
            if (isReconnect_1 && data.users) {
                console.log("断线重连，更新玩家分数和AI托管状态");
                this.updatePlayersDataOnReconnect(data.users);
            }
        }
        // 根据是否是断线重连决定是否显示游戏开始动画
        var isReconnect = data.mapData && (Array.isArray(data.mapData) || typeof data.mapData === 'object');
        if (isReconnect) {
            // 断线重连时隐藏游戏开始节点，因为游戏已经在进行中
            this.hideGameStartAnimation();
            console.log("断线重连：游戏状态为", data.gameStatus);
            // 注意：断线重连时的头像恢复主要依赖于后续的NoticeRoundEnd或NoticeActionDisplay消息
            // 这里不需要特殊处理头像，因为ensureAllPlayerAvatarsExist会在需要时处理
        }
        else {
            // 新游戏时显示游戏开始节点动画
            this.showGameStartAnimation();
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 隐藏游戏开始节点
        this.hideGameStartAnimation();
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        // 保存当前NoticeActionDisplay数据，用于倒计时显示逻辑
        this.currentNoticeActionData = data;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作（头像）
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 立即显示先手+1（如果先手不是我）
        this.showFirstChoiceBonusImmediately(data.playerActions);
    };
    /**
     * 立即显示先手+1奖励（只为其他人显示，如果我是先手则不显示）
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.showFirstChoiceBonusImmediately = function (playerActions) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        // 如果先手玩家存在且不是我，才显示+1
        if (firstChoicePlayer && firstChoicePlayer.userId !== currentUserId) {
            var firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex !== -1) {
                // 立即显示先手+1
                this.showScoreInScorePanel(firstChoiceUserIndex, 1);
                // 同时在player_game_pfb显示先手+1
                this.showScoreOnPlayerAvatar(firstChoicePlayer.userId, 1);
            }
        }
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 注意：分数动画和头像删除现在由倒计时逻辑控制，这里直接处理格子隐藏和数字生成
        var _this = this;
        // 立即处理每个玩家的操作结果
        // 先按位置分组，处理同一位置有多个操作的情况
        var processedPositions = new Set();
        data.playerActions.forEach(function (action) {
            var positionKey = action.x + "," + action.y;
            // 如果这个位置已经处理过，跳过
            if (processedPositions.has(positionKey)) {
                return;
            }
            // 查找同一位置的所有操作
            var samePositionActions = data.playerActions.filter(function (a) {
                return a.x === action.x && a.y === action.y;
            });
            // 处理同一位置的操作结果（格子隐藏和数字生成）
            _this.processPositionResult(action.x, action.y, samePositionActions);
            // 标记这个位置已处理
            processedPositions.add(positionKey);
        });
        // 处理连锁展开结果
        if (data.floodFillResults && data.floodFillResults.length > 0) {
            data.floodFillResults.forEach(function (floodFill) {
                _this.processFloodFillResult(floodFill);
            });
        }
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _this = this;
        var _a, _b;
        // 根据地图类型删除该位置的格子（播放动画）
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y, false);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y, false);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：延迟更新neighborMines显示，等动画完成
            this.scheduleOnce(function () {
                if (_this.currentMapType === 0) {
                    // 方形地图
                    _this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
                }
                else if (_this.currentMapType === 1) {
                    // 六边形地图
                    if (_this.hexChessBoardController) {
                        _this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                    }
                }
            }, 0.45); // 等待动画完成（0.15秒上升 + 0.3秒下落）
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 同时播放所有连锁格子的消失动画
        floodFill.revealedBlocks.forEach(function (block) {
            if (_this.currentMapType === 0) {
                // 方形地图：播放消失动画
                _this.chessBoardController.removeGridAt(block.x, block.y, false);
                // 延迟显示数字，等动画完成
                _this.scheduleOnce(function () {
                    if (block.neighborMines > 0) {
                        _this.chessBoardController.updateNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                    }
                }, 0.45); // 等待动画完成（0.15秒上升 + 0.3秒下落）
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 播放消失动画
                    _this.hexChessBoardController.hideHexGridAt(block.x, block.y, false);
                    // 延迟显示数字，等动画完成
                    _this.scheduleOnce(function () {
                        if (block.neighborMines > 0) {
                            _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                        }
                    }, 0.45); // 等待动画完成
                }
            }
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            // 先确保所有玩家的头像都存在（包括断线重连的玩家）
            this.ensureAllPlayerAvatarsExist(data.playerResults);
            // 然后显示分数动画
            this.displayPlayerScoreAnimations(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 确保所有玩家的头像都存在（包括断线重连的玩家）
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.ensureAllPlayerAvatarsExist = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 检查游戏状态：如果是回合结束展示阶段（gameStatus=2），不生成头像预制体
        // 根据API.md定义：2 = 回合结束展示（仅联机模式）
        if (this.gameStatus === 2) {
            console.log("\u5F53\u524D\u6E38\u620F\u72B6\u6001\u4E3A" + this.gameStatus + "\uFF08\u56DE\u5408\u7ED3\u675F\u5C55\u793A\u9636\u6BB5\uFF09\uFF0C\u8DF3\u8FC7\u5934\u50CF\u751F\u6210\u4EE5\u907F\u514D\u91CD\u590D\u663E\u793A");
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        console.log("\u6E38\u620F\u72B6\u6001\u4E3A" + this.gameStatus + "\uFF0C\u5F00\u59CB\u68C0\u67E5\u5E76\u751F\u6210\u7F3A\u5931\u7684\u5934\u50CF");
        // 为每个玩家检查并生成头像
        playerResults.forEach(function (result) {
            var isMyself = result.userId === currentUserId;
            // 检查格子上是否已经有头像
            var hasExistingAvatar = false;
            if (_this.currentMapType === 0 && _this.chessBoardController) {
                // 方形地图：检查格子是否已有玩家
                hasExistingAvatar = !_this.chessBoardController.isGridEmpty(result.x, result.y);
            }
            else if (_this.currentMapType === 1 && _this.hexChessBoardController) {
                // 六边形地图：检查格子是否已有玩家
                hasExistingAvatar = !_this.hexChessBoardController.isHexGridEmpty(result.x, result.y);
            }
            // 如果格子上没有头像，则生成头像
            if (!hasExistingAvatar) {
                console.log("\u4E3A" + (isMyself ? '自己' : '其他玩家') + "\u5728\u683C\u5B50(" + result.x + ", " + result.y + ")\u751F\u6210\u5934\u50CF");
                // 根据操作类型决定是否显示旗子
                var withFlag = (result.action === 2); // action=2表示标记操作，显示旗子
                // 根据地图类型生成头像预制体
                // 注意：这里使用bypassGameStatusCheck=true，因为这是在回合结束时为断线重连用户补偿显示头像
                if (_this.currentMapType === 0 && _this.chessBoardController) {
                    // 方形地图：对于自己和其他玩家都使用相同的方法
                    _this.chessBoardController.placePlayerOnGrid(result.x, result.y, withFlag, true);
                }
                else if (_this.currentMapType === 1 && _this.hexChessBoardController) {
                    // 六边形地图：对于自己和其他玩家都使用相同的方法
                    // x实际是q坐标，y实际是r坐标
                    _this.hexChessBoardController.placePlayerOnHexGrid(result.x, result.y, withFlag, true);
                }
            }
        });
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像（已废弃，由ensureAllPlayerAvatarsExist替代）
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        // 这个方法已经被ensureAllPlayerAvatarsExist替代，保留是为了兼容性
        console.log("handleMyAvatarIfNotOperated已被ensureAllPlayerAvatarsExist替代");
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 处理AI托管状态变更通知
     * @param data AIStatusChange 消息数据
     */
    GamePageController.prototype.onAIStatusChange = function (data) {
        var _a, _b;
        var userId = data.userId, isAIManaged = data.isAIManaged;
        // 转发给GameScoreController处理托管状态显示
        if (this.gameScoreController) {
            this.gameScoreController.onAIStatusChange(userId, isAIManaged);
        }
        // 检查是否为当前用户的托管状态变更
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (userId === currentUserId) {
            // 更新当前用户的托管状态
            this.isCurrentUserAIManaged = isAIManaged;
            if (isAIManaged) {
                // 当前用户进入AI托管，显示托管页面
                this.showAIManagedDialog();
            }
            else {
                // 当前用户退出AI托管，隐藏托管页面
                this.hideAIManagedDialog();
            }
        }
    };
    /**
     * 显示AI托管页面
     */
    GamePageController.prototype.showAIManagedDialog = function () {
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.show();
        }
        else {
            console.warn("❌ AI托管页面控制器未设置，请在编辑器中设置 aiManagedDialogController 属性");
        }
    };
    /**
     * 隐藏AI托管页面
     */
    GamePageController.prototype.hideAIManagedDialog = function () {
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.hide();
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 根据地图类型调用对应的控制器显示加分效果
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("\u5730\u56FE\u7C7B\u578B" + this.currentMapType + "\u7684\u68CB\u76D8\u63A7\u5236\u5668\u672A\u8BBE\u7F6E\uFF0C\u65E0\u6CD5\u663E\u793Aplayer_game_pfb\u52A0\u5206\u6548\u679C");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    GamePageController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onGamePageClick, this);
        }
        // 清理棋盘事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.off('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
        // 清理倒计时
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        // 停止所有动画
        this.stopAllAnimations();
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            // 在NoticeActionDisplay阶段，根据倒计时执行不同的显示逻辑
            if (_this.gameStatus === 0 && _this.currentNoticeActionData) {
                _this.handleCountdownBasedDisplay(remainingSeconds);
            }
            else {
            }
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    /**
     * 根据倒计时处理不同时机的显示逻辑
     * @param remainingSeconds 剩余秒数
     */
    GamePageController.prototype.handleCountdownBasedDisplay = function (remainingSeconds) {
        if (!this.currentNoticeActionData) {
            return;
        }
        var data = this.currentNoticeActionData;
        if (remainingSeconds === 4) {
            // 4s时：同时展示本回合加减分
            this.showCurrentRoundScores(data.playerActions, data.playerTotalScores);
        }
        else if (remainingSeconds === 3) {
            // 3s时：隐藏加减分并删除头像预制体
            this.hideScoreEffectsAndAvatars(data.playerActions);
            // 3s时：立即执行格子隐藏和生成数字预制体等操作
            this.updateBoardAfterActions(data);
        }
        else if (remainingSeconds === 2) {
            // 2s时：检查游戏是否结束，如果没结束才显示回合开始节点动画
            if (data.isGameEnded) {
                console.log("游戏已结束，不显示回合开始动画");
            }
            else {
                console.log("游戏未结束，显示回合开始动画");
                this.showRoundStartAnimation();
            }
            // 在2s时清空数据，避免重复执行
            this.currentNoticeActionData = null;
        }
    };
    /**
     * 显示本回合所有玩家的加减分
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.showCurrentRoundScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示本回合的加减分
        playerActions.forEach(function (action) {
            // 在player_game_pfb中显示本回合的加减分
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            // 在分数面板显示本回合的加减分
            var userIndex = _this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        });
        // 延迟更新总分，让加减分动画先显示
        this.scheduleOnce(function () {
            // 更新所有玩家的总分
            playerActions.forEach(function (action) {
                var totalScore = playerTotalScores[action.userId] || 0;
                if (_this.gameScoreController) {
                    _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                    // 更新全局数据中的总分
                    _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                }
            });
        }, 1.2);
    };
    /**
     * 隐藏加减分效果并删除头像预制体
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.hideScoreEffectsAndAvatars = function (playerActions) {
        // 隐藏所有加减分效果
        this.hideAllScoreEffects();
        // 删除头像预制体（不等待完成回调）
        this.hideAllAvatars(playerActions, function () {
        });
    };
    /**
     * 隐藏所有加减分效果
     */
    GamePageController.prototype.hideAllScoreEffects = function () {
        // 隐藏分数面板的加减分效果
        // 注意：这里暂时不处理分数面板的隐藏，因为PlayerScoreController的hideScoreEffects会在1秒后自动隐藏
        // 隐藏棋盘上所有头像的加减分效果
        this.hideAllPlayerGameScoreEffects();
    };
    /**
     * 隐藏棋盘上所有头像的加减分效果
     */
    GamePageController.prototype.hideAllPlayerGameScoreEffects = function () {
        // 遍历棋盘上的所有PlayerGameController，调用hideScoreEffects方法
        if (this.currentMapType === 0 && this.chessBoardController && this.chessBoardController.boardNode) {
            // 方形地图
            var children = this.chessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController && this.hexChessBoardController.boardNode) {
            // 六边形地图
            var children = this.hexChessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        else {
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 初始化动画节点
     */
    GamePageController.prototype.initializeAnimationNodes = function () {
        // 如果节点没有在编辑器中设置，尝试通过路径查找或创建
        if (!this.gameStartNode) {
            this.gameStartNode = cc.find('Canvas/game_start_node');
            if (!this.gameStartNode) {
                // 创建游戏开始节点
                this.gameStartNode = this.createGameStartNode();
            }
        }
        if (!this.roundStartNode) {
            this.roundStartNode = cc.find('Canvas/round_start_node');
            if (!this.roundStartNode) {
                // 创建回合开始节点
                this.roundStartNode = this.createRoundStartNode();
            }
        }
        // 初始状态设为隐藏
        if (this.gameStartNode) {
            this.gameStartNode.active = false;
        }
        if (this.roundStartNode) {
            this.roundStartNode.active = false;
        }
    };
    /**
     * 创建游戏开始节点
     */
    GamePageController.prototype.createGameStartNode = function () {
        var node = new cc.Node('game_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        // 设置节点位置和层级
        node.setPosition(0, 0);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('开始游戏@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载游戏开始图片资源");
            }
        });
        return node;
    };
    /**
     * 创建回合开始节点
     */
    GamePageController.prototype.createRoundStartNode = function () {
        var node = new cc.Node('round_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        else {
            console.warn("找不到Canvas节点");
        }
        // 设置节点位置和层级
        node.setPosition(-750, -1);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('huihe@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载回合开始图片资源:", err);
            }
        });
        return node;
    };
    /**
     * 显示游戏开始节点动画（放大展示）
     */
    GamePageController.prototype.showGameStartAnimation = function () {
        if (!this.gameStartNode) {
            console.warn("游戏开始节点不存在");
            return;
        }
        // 停止之前可能正在进行的动画
        this.gameStartNode.stopAllActions();
        // 初始化节点状态
        this.gameStartNode.active = true;
        this.gameStartNode.scale = 0;
        this.gameStartNode.opacity = 255;
        // 放大展示动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 1.2 }, { easing: 'backOut' })
            .to(0.2, { scale: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 隐藏游戏开始节点动画（缩小隐藏）
     */
    GamePageController.prototype.hideGameStartAnimation = function () {
        var _this = this;
        if (!this.gameStartNode || !this.gameStartNode.active) {
            return;
        }
        // 停止之前可能正在进行的动画
        this.gameStartNode.stopAllActions();
        // 缩小隐藏动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 0, opacity: 0 }, { easing: 'backIn' })
            .call(function () {
            _this.gameStartNode.active = false;
        })
            .start();
    };
    /**
     * 显示回合开始节点动画（从左边移入到中间）
     */
    GamePageController.prototype.showRoundStartAnimation = function () {
        var _this = this;
        if (!this.roundStartNode) {
            console.warn("回合开始节点不存在");
            return;
        }
        // 停止之前可能正在进行的动画
        this.roundStartNode.stopAllActions();
        // 初始化节点状态：在(-750, -1)位置
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        // 0.5秒移动到中间(0, -1)，1秒展示，0.5秒移动到(750, -1)，然后恢复到(-750, -1)
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .delay(1.0)
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            // 恢复到初始位置(-750, -1)为下一次回合开始做准备
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    /**
     * 停止所有动画并重置动画节点状态
     */
    GamePageController.prototype.stopAllAnimations = function () {
        // 停止回合开始动画
        if (this.roundStartNode) {
            this.roundStartNode.stopAllActions();
            // 重置回合开始节点到初始状态
            this.roundStartNode.active = false;
            this.roundStartNode.setPosition(-750, -1);
            this.roundStartNode.opacity = 255;
            this.roundStartNode.scale = 1;
        }
        // 停止游戏开始动画
        if (this.gameStartNode) {
            this.gameStartNode.stopAllActions();
            // 重置游戏开始节点到初始状态
            this.gameStartNode.active = false;
            this.gameStartNode.scale = 0;
            this.gameStartNode.opacity = 255;
        }
        // 停止AI托管页面动画
        if (this.aiManagedDialogController) {
            this.aiManagedDialogController.hide();
        }
    };
    /**
     * 断线重连时更新玩家数据（分数和AI托管状态）
     * @param users 用户数据数组
     */
    GamePageController.prototype.updatePlayersDataOnReconnect = function (users) {
        var _this = this;
        var _a, _b;
        if (!users || !Array.isArray(users)) {
            console.warn("用户数据无效，无法更新");
            return;
        }
        console.log("更新玩家数据，用户数量:", users.length);
        // 先处理每个玩家的分数同步，然后再更新UI
        users.forEach(function (user, index) {
            // 优先使用gameScore，如果不存在则使用score
            var playerScore = user.gameScore !== undefined ? user.gameScore : (user.score || 0);
            console.log("\u73A9\u5BB6" + index + ": " + user.nickName + " (" + user.userId + ") - \u5206\u6570: " + playerScore + " (gameScore: " + user.gameScore + ", score: " + user.score + "), AI\u6258\u7BA1: " + user.isAIManaged);
            // 同步gameScore到score字段，确保兼容性
            if (user.gameScore !== undefined) {
                user.score = user.gameScore;
                console.log("\u540C\u6B65\u5206\u6570: \u73A9\u5BB6" + index + " " + user.nickName + " \u5206\u6570\u4ECE " + user.gameScore + " \u540C\u6B65\u5230 score \u5B57\u6BB5");
            }
            // 处理AI托管状态
            if (user.isAIManaged !== undefined && _this.gameScoreController) {
                _this.gameScoreController.onAIStatusChange(user.userId, user.isAIManaged);
            }
        });
        // 更新GlobalBean中的用户数据（在分数同步之后）
        if (GlobalBean_1.GlobalBean.GetInstance().noticeStartGame) {
            GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users = users;
        }
        // 通过GameScoreController更新分数显示（在分数同步之后）
        if (this.gameScoreController) {
            // 重新设置游戏数据，这会更新所有玩家的分数显示
            this.gameScoreController.setGameData();
            console.log("分数UI已更新");
        }
        // 检查当前用户是否处于AI托管状态
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var currentUser = users.find(function (user) { return user.userId === currentUserId; });
        if (currentUser && currentUser.isAIManaged === true) {
            console.log("当前用户处于AI托管状态，显示托管UI");
            // 更新当前用户的托管状态
            this.isCurrentUserAIManaged = true;
            // 延迟显示托管UI，确保界面初始化完成
            this.scheduleOnce(function () {
                _this.showAIManagedDialog();
            }, 0.3);
            // 同时更新GameScoreController中的托管状态显示
            if (this.gameScoreController) {
                this.gameScoreController.onAIStatusChange(currentUserId, true);
            }
        }
        else {
            // 确保托管状态被正确重置
            this.isCurrentUserAIManaged = false;
            this.hideAIManagedDialog();
            // 同时更新GameScoreController中的托管状态显示
            if (this.gameScoreController && currentUserId) {
                this.gameScoreController.onAIStatusChange(currentUserId, false);
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(AIManagedDialogController_1.default)
    ], GamePageController.prototype, "aiManagedDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "gameStartNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "roundStartNode", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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