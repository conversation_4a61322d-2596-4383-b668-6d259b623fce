// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import NickNameLabel from "../util/NickNameLabel";
import { Tools } from "../util/Tools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PlayerScoreController extends cc.Component {

    @property(cc.Node)
    avatar: cc.Node = null;   //头像
    @property(cc.Label)
    nameLabel: cc.Label = null;  //用户昵称

    // 分数显示相关节点
    @property(cc.Node)
    scoreBgMy: cc.Node = null;  //我的分数背景节点 score_bg_my
    @property(cc.Node)
    scoreBgOthers: cc.Node = null;  //其他人的分数背景节点 score_bg_others

    // 加减分效果节点
    @property(cc.Node)
    addScoreNode: cc.Node = null;  //加分背景节点 addscore
    @property(cc.Node)
    subScoreNode: cc.Node = null;  //减分背景节点 deductscore

    // AI托管显示节点
    @property(cc.Node)
    aiManagedNode: cc.Node = null;  //AI托管显示节点

    // 当前用户数据
    private currentUser: RoomUser = null;

    start () {
    
        // 初始化时隐藏所有加减分效果
        this.hideScoreEffects();
        // 不要默认隐藏AI托管节点，让setData方法来决定是否显示
        // this.hideAIManagedNode();
    }

    /**
     * 设置玩家数据
     * @param user 房间用户数据
     */
    setData(user: RoomUser) {
        this.currentUser = user;

        if (user == null) {
            // 清空数据
            this.avatar.active = false;
            this.nameLabel.string = "";
            this.hideAllScoreBackgrounds();
            this.hideScoreEffects();
            this.hideAIManagedNode();
        } else {
            // 设置头像和昵称
            Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);
            let nicknameLabel = this.nameLabel.getComponent(NickNameLabel);
            nicknameLabel.string = user.nickName;
            this.avatar.active = true;

            // 设置分数显示
            this.updateScore(user.score || 0);

            // 设置AI托管状态显示
            if (user.isAIManaged !== undefined) {
               
                this.setAIManagedStatus(user.isAIManaged);
            } else {
                // 如果没有AI托管状态信息，默认隐藏托管节点
                this.hideAIManagedNode();
            }
        }
    }

    /**
     * 更新分数显示
     * @param score 新的分数值
     */
    updateScore(score: number) {
        if (!this.currentUser) return;

        const isMyself = this.isCurrentUser(this.currentUser.userId);

        if (isMyself) {
            // 显示我的分数
            this.showMyScore(score);
        } else {
            // 显示其他人的分数
            this.showOthersScore(score);
        }
    }

    /**
     * 判断是否为当前登录用户
     * @param userId 用户ID
     */
    private isCurrentUser(userId: string): boolean {
        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;
        return userId === currentUserId;
    }

    /**
     * 显示我的分数
     * @param score 分数值
     */
    private showMyScore(score: number) {
        // 显示我的分数背景，隐藏其他人的
        if (this.scoreBgMy) {
            this.scoreBgMy.active = true;
            // 获取my_score文本节点并设置分数
            const myScoreLabel = this.scoreBgMy.getChildByName("my_score");
            if (myScoreLabel) {
                const labelComponent = myScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }

        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    }

    /**
     * 显示其他人的分数
     * @param score 分数值
     */
    private showOthersScore(score: number) {
        // 显示其他人的分数背景，隐藏我的
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = true;
            // 获取other_score文本节点并设置分数
            const otherScoreLabel = this.scoreBgOthers.getChildByName("other_score");
            if (otherScoreLabel) {
                const labelComponent = otherScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = score.toString();
                }
            }
        }

        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
    }

    /**
     * 隐藏所有分数背景
     */
    private hideAllScoreBackgrounds() {
        if (this.scoreBgMy) {
            this.scoreBgMy.active = false;
        }
        if (this.scoreBgOthers) {
            this.scoreBgOthers.active = false;
        }
    }

    /**
     * 显示加分效果
     * @param addValue 加分数值
     */
    showAddScore(addValue: number) {
        if (this.addScoreNode) {
            this.addScoreNode.active = true;

            // 获取change_score文本节点并设置加分文本
            const changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                const labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }

            // 1秒后隐藏
            this.scheduleOnce(() => {
                if (this.addScoreNode) {
                    this.addScoreNode.active = false;
                }
            }, 1.0);
        }
    }

    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    showSubScore(subValue: number) {
        if (this.subScoreNode) {
            this.subScoreNode.active = true;

            // 获取change_score文本节点并设置减分文本
            const changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                const labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }

            // 1秒后隐藏
            this.scheduleOnce(() => {
                if (this.subScoreNode) {
                    this.subScoreNode.active = false;
                }
            }, 1.0);
        }
    }

    /**
     * 隐藏加减分效果节点
     */
    hideScoreEffects() {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    }

    /**
     * 显示AI托管节点
     */
    showAIManagedNode() {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = true;        
        }
    }

    /**
     * 隐藏AI托管节点
     */
    hideAIManagedNode() {
        if (this.aiManagedNode) {
            this.aiManagedNode.active = false;
        }
    }

    /**
     * 设置AI托管状态
     * @param isManaged 是否进入AI托管状态
     */
    setAIManagedStatus(isManaged: boolean) {
        
        if (isManaged) {
            this.showAIManagedNode();
            

            // 强制刷新节点显示状态
            if (this.aiManagedNode) {
                this.scheduleOnce(() => {
                   

                   
                    // 尝试强制刷新
                    this.aiManagedNode.active = false;
                    this.aiManagedNode.active = true;

                    // 再次检查状态
                   
                }, 0.1);
            }
        } else {
            this.hideAIManagedNode();
           
        }
    }
}
